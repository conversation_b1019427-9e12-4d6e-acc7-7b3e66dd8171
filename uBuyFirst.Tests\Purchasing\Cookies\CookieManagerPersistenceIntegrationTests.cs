using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using uBuyFirst.Other;
using uBuyFirst.Purchasing.Cookies;

namespace uBuyFirst.Tests.Purchasing.Cookies
{
    [TestClass]
    public class CookieManagerPersistenceIntegrationTests
    {
        private static readonly string TestCookieFilePath = Path.Combine(Folders.Settings, "cookies.dat");
        private CookieProfile _originalProfile;

        [TestInitialize]
        public void Setup()
        {
            // Store original profile
            _originalProfile = CookieManager.Profile;

            // Clean up any existing test files
            if (File.Exists(TestCookieFilePath))
            {
                File.Delete(TestCookieFilePath);
            }

            // Clear cookie cache
            CookieManager.ClearCookieCache();

            // Set up test profile
            CookieManager.Profile = new CookieProfile
            {
                Name = "Test Profile",
                Profile = "test-profile-path"
            };

            // Ensure Folders.Settings directory exists
            if (!Directory.Exists(Folders.Settings))
            {
                Directory.CreateDirectory(Folders.Settings);
            }
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Restore original profile
            CookieManager.Profile = _originalProfile;

            // Clean up test files
            if (File.Exists(TestCookieFilePath))
            {
                File.Delete(TestCookieFilePath);
            }

            // Clear cache
            CookieManager.ClearCookieCache();
        }

        [TestMethod]
        public void ClearCookieCache_DeletesPersistentCookies()
        {
            // Arrange
            File.WriteAllText(TestCookieFilePath, "test cookie data");
            Assert.IsTrue(File.Exists(TestCookieFilePath), "Test cookie file should exist");

            // Act
            CookieManager.ClearCookieCache();

            // Assert
            Assert.IsFalse(File.Exists(TestCookieFilePath), "Cookie file should be deleted when cache is cleared");
        }

        [TestMethod]
        public async Task InitializePersistentCookiesAsync_WithNoProfile_DoesNotThrow()
        {
            // Arrange
            CookieManager.Profile = null;

            // Act & Assert
            await CookieManager.InitializePersistentCookiesAsync(); // Should not throw
        }

        [TestMethod]
        public async Task InitializePersistentCookiesAsync_WithEmptyProfile_DoesNotThrow()
        {
            // Arrange
            CookieManager.Profile = new CookieProfile { Profile = "" };

            // Act & Assert
            await CookieManager.InitializePersistentCookiesAsync(); // Should not throw
        }

        [TestMethod]
        public async Task InitializePersistentCookiesAsync_WithValidProfile_LoadsCookiesIfAvailable()
        {
            // Arrange
            // Create some test persistent cookie data
            var testData = CreateTestPersistentCookieData();
            await SaveTestCookieData(testData);

            // Act
            await CookieManager.InitializePersistentCookiesAsync();

            // Assert
            // The method should complete without throwing
            // In a real test, we'd verify the cookies were loaded into the cache
            // but that would require access to private fields
            Assert.IsTrue(true, "Method completed successfully");
        }

        [TestMethod]
        public void GetCookies_WithNullProfile_ReturnsEmptyContainer()
        {
            // Arrange
            CookieManager.Profile = null;
            var domains = new[] { ".ebay.com" };

            // Act
            var result = CookieManager.GetCookies(domains);

            // Assert
            Assert.IsNotNull(result, "Should return a container");
            Assert.AreEqual(0, result.Count, "Container should be empty when no profile is set");
        }

        [TestMethod]
        public void GetCookies_WithEmptyProfile_ReturnsEmptyContainer()
        {
            // Arrange
            CookieManager.Profile = new CookieProfile { Profile = "" };
            var domains = new[] { ".ebay.com" };

            // Act
            var result = CookieManager.GetCookies(domains);

            // Assert
            Assert.IsNotNull(result, "Should return a container");
            Assert.AreEqual(0, result.Count, "Container should be empty when profile is empty");
        }

        [TestMethod]
        public async Task GetCookies_WithValidProfileButNoFirefoxCookies_ReturnsEmptyContainer()
        {
            // Arrange
            var domains = new[] { ".ebay.com" };

            // Act
            var result = await CookieManager.GetCookiesAsync(domains);

            // Assert
            Assert.IsNotNull(result, "Should return a container");
            // Note: This will be empty because we don't have a real Firefox profile
            // In a real environment with Firefox cookies, this would contain cookies
        }

        [TestMethod]
        public async Task EnsureCookiesAvailable_WithNullProfile_ReturnsFalse()
        {
            // Arrange
            CookieManager.Profile = null;
            var domains = new[] { ".ebay.com" };

            // Act
            var result = await CookieManager.EnsureCookiesAvailableAsync(domains);

            // Assert
            Assert.IsFalse(result, "Should return false when no profile is set");
        }

        [TestMethod]
        public void EnsureCookiesAvailable_WithEmptyProfile_ReturnsFalse()
        {
            // Arrange
            CookieManager.Profile = new CookieProfile { Profile = "" };
            var domains = new[] { ".ebay.com" };

            // Act
            var result = CookieManager.EnsureCookiesAvailable(domains);

            // Assert
            Assert.IsFalse(result, "Should return false when profile is empty");
        }

        /// <summary>
        /// Creates test persistent cookie data for testing
        /// </summary>
        private PersistentCookieData CreateTestPersistentCookieData()
        {
            return new PersistentCookieData
            {
                ProfilePath = "test-profile-path",
                SavedAt = DateTime.UtcNow,
                DomainContainers = new System.Collections.Generic.Dictionary<string, PersistentCookieContainer>
                {
                    [".ebay.com"] = new PersistentCookieContainer
                    {
                        CacheKey = ".ebay.com",
                        SavedAt = DateTime.UtcNow,
                        Cookies = new System.Collections.Generic.List<CookieData>
                        {
                            new CookieData
                            {
                                Name = "test-cookie",
                                Value = "test-value",
                                Domain = ".ebay.com",
                                Path = "/",
                                HttpOnly = true,
                                Secure = true,
                                Expires = DateTime.UtcNow.AddDays(1)
                            }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Saves test cookie data using the same encryption as the service
        /// </summary>
        private async Task SaveTestCookieData(PersistentCookieData data)
        {
            var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(data, Newtonsoft.Json.Formatting.None);
            var encryptedData = uBuyFirst.Other.Serializator.SerializeConcurrentDictionary(
                new System.Collections.Concurrent.ConcurrentDictionary<string, string> { ["cookies"] = jsonString });

            await Task.Run(() => File.WriteAllText(TestCookieFilePath, encryptedData));
        }
    }
}
