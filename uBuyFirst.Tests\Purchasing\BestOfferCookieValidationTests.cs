using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Reflection;
using uBuyFirst.Purchasing;

namespace uBuyFirst.Tests.Purchasing
{
    [TestClass]
    public class BestOfferCookieValidationTests
    {
        [TestMethod]
        public void IsLogoutDetected_WithJsonRedirectResponse_ReturnsTrue()
        {
            // Arrange
            var jsonResponse = @"{""status"":""REDIRECT"",""redirectUrl"":""https://signin.ebay.com/ws/eBayISAPI.dll?SignIn&UsingSSL=1&siteid=0&co_partnerId=2&pageType=3765031&ru=...""}";

            // Act
            var result = CallPrivateIsLogoutDetected(jsonResponse);

            // Assert
            Assert.IsTrue(result, "Should detect logout from JSON redirect response");
        }

        [TestMethod]
        public void IsLogoutDetected_WithSigninUrlInContent_ReturnsTrue()
        {
            // Arrange
            var htmlContent = @"<html><body>Please sign in at https://signin.ebay.com/ws/eBayISAPI.dll?SignIn to continue</body></html>";

            // Act
            var result = CallPrivateIsLogoutDetected(htmlContent);

            // Assert
            Assert.IsTrue(result, "Should detect logout from signin URL in HTML content");
        }

        [TestMethod]
        public void IsLogoutDetected_WithNormalResponse_ReturnsFalse()
        {
            // Arrange
            var normalResponse = @"{""model"":{""modules"":{""OFFER_ACTIONS"":{""offerActions"":[{""action"":{""name"":""SEND_OFFER""}}]}}}}";

            // Act
            var result = CallPrivateIsLogoutDetected(normalResponse);

            // Assert
            Assert.IsFalse(result, "Should not detect logout from normal response");
        }

        [TestMethod]
        public void IsLogoutDetected_WithEmptyContent_ReturnsFalse()
        {
            // Arrange
            string emptyContent = "";

            // Act
            var result = CallPrivateIsLogoutDetected(emptyContent);

            // Assert
            Assert.IsFalse(result, "Should not detect logout from empty content");
        }

        [TestMethod]
        public void IsLogoutDetected_WithNullContent_ReturnsFalse()
        {
            // Arrange
            string nullContent = null;

            // Act
            var result = CallPrivateIsLogoutDetected(nullContent);

            // Assert
            Assert.IsFalse(result, "Should not detect logout from null content");
        }

        [TestMethod]
        public void IsLogoutError_WithLogoutMessage_ReturnsTrue()
        {
            // Arrange
            var logoutMessage = "User logged out - signin redirect detected";

            // Act
            var result = CallPrivateIsLogoutError(logoutMessage);

            // Assert
            Assert.IsTrue(result, "Should detect logout from logout error message");
        }

        [TestMethod]
        public void IsLogoutError_WithNormalMessage_ReturnsFalse()
        {
            // Arrange
            var normalMessage = "HTTP Error: 500";

            // Act
            var result = CallPrivateIsLogoutError(normalMessage);

            // Assert
            Assert.IsFalse(result, "Should not detect logout from normal error message");
        }

        /// <summary>
        /// Helper method to call the private IsLogoutDetected method in BestOfferService using reflection
        /// </summary>
        private bool CallPrivateIsLogoutDetected(string responseContent)
        {
            var type = typeof(BestOfferService);
            var method = type.GetMethod("IsLogoutDetected", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.IsNotNull(method, "IsLogoutDetected method should exist");
            
            var result = method.Invoke(null, new object[] { responseContent });
            return (bool)result;
        }

        /// <summary>
        /// Helper method to call the private IsLogoutError method in BestOfferCheckout using reflection
        /// </summary>
        private bool CallPrivateIsLogoutError(string errorMessage)
        {
            var type = typeof(BestOfferCheckout);
            var method = type.GetMethod("IsLogoutError", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.IsNotNull(method, "IsLogoutError method should exist");
            
            var result = method.Invoke(null, new object[] { errorMessage });
            return (bool)result;
        }
    }
}
