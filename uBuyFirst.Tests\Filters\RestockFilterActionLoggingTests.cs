﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.Intl;
using uBuyFirst.Pricing;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Filters
{
    [TestClass]
    public class RestockFilterActionLoggingTests
    {
        private RestockFilterAction _action;

        [TestInitialize]
        public void TestInitialize()
        {
            _action = new RestockFilterAction();
        }

        [TestCleanup]
        public void TestCleanup()
        {
            _action?.Dispose();
        }

        /// <summary>
        /// Creates a test EBaySite for testing purposes
        /// </summary>
        private static EBaySite CreateTestEBaySite()
        {
            // Use the US eBay site format from CountryProvider
            return new EBaySite("EBAY-US\tEBAY_US\ten-US\tUS\teBay US\t0\tUS\tebay.com\tpicsuffix\t1\thttps://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466");
        }

        /// <summary>
        /// Creates a test DataList for testing purposes
        /// </summary>
        private static DataList CreateTestDataList()
        {
            return new DataList
            {
                ItemID = "123456789",
                Title = "Test Item for Logging",
                Term = "test-keyword",
                QuantityAvailable = 5,
                ItemPricing = new ItemPricing { ItemPrice = new CurrencyAmount(25.99, "USD") },
                EBaySite = CreateTestEBaySite()
            };
        }

        [TestMethod]
        public async Task LogPurchaseAttemptAsync_WithSuccessfulPurchase_ShouldLogCorrectly()
        {
            // Arrange
            var dataList = CreateTestDataList();
            var filterAlias = "test-restock-filter";
            var purchaseResult = PurchaseExecutionResult.CreateSuccess("Successfully purchased 2 items", 2);

            // Act
            await _action.LogPurchaseAttemptAsync(dataList, filterAlias, purchaseResult);

            // Assert
            // This test verifies that the method doesn't throw exceptions
            // In a real scenario, you would verify the log file was created with correct content
            Assert.IsTrue(true, "LogPurchaseAttemptAsync should complete without exceptions");
        }

        [TestMethod]
        public async Task LogPurchaseAttemptAsync_WithFailedPurchase_ShouldLogCorrectly()
        {
            // Arrange
            var dataList = CreateTestDataList();
            var filterAlias = "test-restock-filter";
            var purchaseResult = PurchaseExecutionResult.CreateFailure("Payment failed due to insufficient funds");

            // Act
            await _action.LogPurchaseAttemptAsync(dataList, filterAlias, purchaseResult);

            // Assert
            // This test verifies that the method doesn't throw exceptions
            // In a real scenario, you would verify the log file was created with correct content
            Assert.IsTrue(true, "LogPurchaseAttemptAsync should complete without exceptions for failed purchases");
        }

        [TestMethod]
        public async Task LogPurchaseAttemptAsync_WithSkippedPurchase_ShouldLogCorrectly()
        {
            // Arrange
            var dataList = CreateTestDataList();
            var filterAlias = "test-restock-filter";
            var purchaseResult = PurchaseExecutionResult.CreateSkipped("Required quantity already reached");

            // Act
            await _action.LogPurchaseAttemptAsync(dataList, filterAlias, purchaseResult);

            // Assert
            // This test verifies that the method doesn't throw exceptions
            // In a real scenario, you would verify the log file was created with correct content
            Assert.IsTrue(true, "LogPurchaseAttemptAsync should complete without exceptions for skipped purchases");
        }

        [TestMethod]
        public void SuccessfulPurchaseOutcome_ShouldBeRecognized()
        {
            // Arrange
            var successResult = PurchaseExecutionResult.CreateSuccess("Successfully purchased 2 items", 2);

            // Act
            var outcome = uBuyFirst.RestockReporting.Services.DataListMapper.DetermineOutcome(true, successResult);

            // Assert
            Assert.AreEqual("purchased", outcome, "Successful purchases should have 'purchased' outcome");
        }

        [TestMethod]
        public void PurchaseFailureOutcome_ShouldBeRecognized()
        {
            // Arrange
            var failureResult = PurchaseExecutionResult.CreateFailure("Payment failed due to insufficient funds");

            // Act
            var outcome = uBuyFirst.RestockReporting.Services.DataListMapper.DetermineOutcome(true, failureResult);

            // Assert
            Assert.AreEqual("purchase_failed", outcome, "Failed purchases should have 'purchase_failed' outcome");
        }
    }
}
