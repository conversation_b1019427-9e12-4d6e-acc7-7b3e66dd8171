﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Purchasing.Cookies;
using System.Linq;
using System.Threading.Tasks;

namespace uBuyFirst.Tests.Purchasing.Cookies
{
    [TestClass]
    public class CookieManagerFirstRunTests
    {
        [TestInitialize]
        public void Setup()
        {
            // Clear cache before each test to simulate first run
            CookieManager.ClearCookieCache();
        }

        [TestMethod]
        public async Task GetCookies_OnFirstRun_WithoutProfile_ReturnsEmptyContainer()
        {
            // Arrange
            CookieManager.Profile = null;
            var hostNames = new[] { ".ebay.com" };

            // Act
            var result = await CookieManager.GetCookiesAsync(hostNames);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count);
        }

        [TestMethod]
        public async Task EnsureCookiesAvailable_WithoutProfile_ReturnsFalse()
        {
            // Arrange
            CookieManager.Profile = null;
            var hostNames = new[] { ".ebay.com" };

            // Act
            var result = await CookieManager.EnsureCookiesAvailableAsync(hostNames);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task GetCookies_OnFirstRun_WithValidProfile_AttemptsFetchFromFirefox()
        {
            // Arrange
            var mockProfile = new CookieProfile { Profile = "test-profile" };
            CookieManager.Profile = mockProfile;
            var hostNames = new[] { ".ebay.com" };

            // Act & Assert
            // This test would require a valid Firefox profile to fully test
            // For now, just verify the method doesn't throw and returns a container
            try
            {
                var result = await CookieManager.GetCookiesAsync(hostNames);
                Assert.IsNotNull(result);
            }
            catch (System.Exception)
            {
                // Expected if Firefox profile doesn't exist or is invalid
                Assert.IsTrue(true);
            }
        }

        [TestMethod]
        public async Task ClearCookieCache_ClearsAllCachedData_ForcesFirefoxFetchOnNextCall()
        {
            // Arrange
            var hostNames = new[] { ".ebay.com" };

            // Act
            CookieManager.ClearCookieCache();

            // This should trigger Firefox fetch since cache is empty
            var result = await CookieManager.GetCookiesAsync(hostNames);

            // Assert
            Assert.IsNotNull(result);
            // The result will be empty if no profile is set, but method should not throw
        }
    }
}
