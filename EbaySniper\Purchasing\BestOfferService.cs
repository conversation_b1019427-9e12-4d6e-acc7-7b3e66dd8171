using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using uBuyFirst.Prefs;

namespace uBuyFirst.Purchasing
{
    public enum OfferOutcome
    {
        Unknown,
        OfferSent,           // Successfully sent, awaiting response
        OfferAccepted,       // <PERSON><PERSON> accepted the offer
        OfferDeclined,       // <PERSON><PERSON> declined the offer
        OfferCountered,      // <PERSON><PERSON> made a counter offer
        OfferExpired,        // Offer expired
        OfferPending,        // Cannot make offer - already have pending offer
        OfferFailed          // Technical error occurred
    }

    public static class BestOfferService
    {
        // Helper method to extract zipcode from eBay response
        private static string? ExtractShipToLocation(string responseText)
        {
            var match = Regex.Match(responseText, "inputOption\\\\\":\\\\\"(.*?)\\\\\"");

            if (match.Success)
            {
                var inputOptionValue = match.Groups[1].Value;
                return inputOptionValue;
            }

            return null;
        }

        // Helper method to analyze offer outcome and extract additional info
        private static (OfferOutcome outcome, string? message, int? offersRemaining, string? retryUrl) AnalyzeOfferResponse(string? jsonContent)
        {
            if (string.IsNullOrWhiteSpace(jsonContent))
            {
                return (OfferOutcome.Unknown, null, null, null);
            }

            try
            {
                var jsonDoc = JObject.Parse(jsonContent);
                var modules = jsonDoc?["model"]?["modules"];

                // Extract status header message
                var statusHeaderText = modules?["OFFER_STATUS_HEADER"]?["header"]?["textSpans"]?[0]?["text"]?.Value<string>();

                // Also check for OFFER_STATUS_MESSAGE (for errors like pending offers)
                var statusMessageText = modules?["OFFER_STATUS_MESSAGE"]?["messages"]?[0]?["additionalText"]?[0]?["textSpans"]?[0]?["text"]?.Value<string>();

                // Use whichever message we found
                var statusText = statusHeaderText ?? statusMessageText;

                // Extract remaining offers from OFFER_SUBMITTED_INFO
                int? offersRemaining = null;
                var offerDetails = modules?["OFFER_SUBMITTED_INFO"]?["offerDetails"] as JArray;
                if (offerDetails != null)
                {
                    foreach (var detail in offerDetails)
                    {
                        var label = detail?["labels"]?[0]?["textSpans"]?[0]?["text"]?.Value<string>();
                        if (label?.Contains("Offers left") == true)
                        {
                            var value = detail?["values"]?[0]?["textSpans"]?[0]?["text"]?.Value<string>();
                            if (value != null && value.Contains(" of "))
                            {
                                var parts = value.Split(new string[] { " of " }, StringSplitOptions.None);
                                if (parts.Length == 2 && int.TryParse(parts[0], out int remaining))
                                {
                                    offersRemaining = remaining;
                                }
                            }
                        }
                    }
                }

                // Extract retry URL from OFFER_ACTIONS
                string? retryUrl = null;
                var offerActions = modules?["OFFER_ACTIONS"]?["offerActions"] as JArray;
                var makeOfferAction = offerActions?.FirstOrDefault(action =>
                    action?["action"]?["name"]?.Value<string>() == "MAKE_OFFER");
                if (makeOfferAction != null)
                {
                    retryUrl = makeOfferAction?["action"]?["URL"]?.Value<string>();
                }

                // Determine outcome based on status message
                OfferOutcome outcome = OfferOutcome.Unknown;
                if (!string.IsNullOrEmpty(statusText))
                {
                    var lowerStatus = statusText.ToLowerInvariant();
                    if (lowerStatus.Contains("declined"))
                        outcome = OfferOutcome.OfferDeclined;
                    else if (lowerStatus.Contains("accepted"))
                        outcome = OfferOutcome.OfferAccepted;
                    else if (lowerStatus.Contains("counter"))
                        outcome = OfferOutcome.OfferCountered;
                    else if (lowerStatus.Contains("expired"))
                        outcome = OfferOutcome.OfferExpired;
                    else if (lowerStatus.Contains("sent") || lowerStatus.Contains("submitted"))
                        outcome = OfferOutcome.OfferSent;
                    else if (lowerStatus.Contains("pending offer") || lowerStatus.Contains("cannot make another offer"))
                        outcome = OfferOutcome.OfferPending;
                    else
                        outcome = OfferOutcome.Unknown;
                }

                return (outcome, statusText, offersRemaining, retryUrl);
            }
            catch (JsonException)
            {
                return (OfferOutcome.OfferFailed, "JSON parsing failed", null, null);
            }
            catch (Exception)
            {
                return (OfferOutcome.OfferFailed, "Error analyzing response", null, null);
            }
        }

        // Helper method to attempt extracting user-facing error messages from JSON
        private static string? ExtractErrorMessageFromJson(string? jsonContent)
        {
            if (string.IsNullOrWhiteSpace(jsonContent))
            {
                return null;
            }

            try
            {
                var jsonDoc = JObject.Parse(jsonContent);

                // Look for common error patterns (adjust based on actual eBay error responses)
                // Example 1: Check for a top-level error message
                var errorMessage = jsonDoc?["errorMessage"]?.Value<string>() ?? jsonDoc?["message"]?.Value<string>();
                if (!string.IsNullOrEmpty(errorMessage)) return errorMessage;

                // Example 2: Check within a specific module like "notifications" or "errorDisplay"
                var notificationMessage = jsonDoc?["model"]?["modules"]?["notifications"]?["messages"]?.FirstOrDefault()?["message"]?.Value<string>() ??
                                          jsonDoc?["model"]?["modules"]?["errorDisplay"]?["message"]?.Value<string>(); // Add more paths as needed
                if (!string.IsNullOrEmpty(notificationMessage)) return notificationMessage;

                // Example 3: Check for validation errors within a specific field structure
                // This requires knowing the structure, e.g., finding the first validation message
                var validationError = jsonDoc.SelectTokens("..validations[*].invalidErrorMessage.title.textSpans[0].text").FirstOrDefault()?.Value<string>() ??
                                     jsonDoc.SelectTokens("..validations[*].maxErrorMessage.title.textSpans[0].text").FirstOrDefault()?.Value<string>() ??
                                     jsonDoc.SelectTokens("..validations[*].minErrorMessage.title.textSpans[0].text").FirstOrDefault()?.Value<string>();

                if (!string.IsNullOrEmpty(validationError)) return validationError;

                // Example 4: Check for OFFER_STATUS_MESSAGE structure (from user feedback)
                var offerStatusMessage = jsonDoc.SelectTokens("model.modules.OFFER_STATUS_MESSAGE.messages[0].additionalText[0].textSpans[0].text").FirstOrDefault()?.Value<string>();
                if (!string.IsNullOrEmpty(offerStatusMessage)) return offerStatusMessage;

                // Example 5: Check for OFFER_STATUS_MESSAGE with elvisMessages (from user feedback)
                var elvisMessageHtml = jsonDoc.SelectTokens("model.modules.OFFER_STATUS_MESSAGE.elvisMessages[0].htmlText").FirstOrDefault()?.Value<string>();
                if (!string.IsNullOrEmpty(elvisMessageHtml))
                {
                    // Attempt to strip HTML tags
                    var plainText = Regex.Replace(elvisMessageHtml, "<.*?>", string.Empty).Trim();
                    // Optionally remove the error code part if present
                    plainText = Regex.Replace(plainText, @"\{e\d+-\d+x\}$", string.Empty).Trim();
                    if (!string.IsNullOrEmpty(plainText)) return plainText;
                }

                // Example 6: Check for OFFER_STATUS_HEADER structure (for declined/success messages)
                var offerStatusHeaderText = jsonDoc.SelectTokens("model.modules.OFFER_STATUS_HEADER.header.textSpans[0].text").FirstOrDefault()?.Value<string>();
                if (!string.IsNullOrEmpty(offerStatusHeaderText)) return offerStatusHeaderText;



                // Add more specific extraction logic here based on observed error formats

            }
            catch (JsonException)
            {
                // JSON parsing failed, content might not be JSON or is malformed
                // Return the raw content snippet if it's not too long? Or just null.
                return jsonContent.Length < 200 ? jsonContent : null; // Avoid returning huge HTML pages
            }
            catch (Exception)
            {
                // Ignore other exceptions during error extraction
            }

            return null; // No specific error message found
        }

        /// <summary>
        /// Handles cookie refresh when logout is detected during best offer operations.
        /// Clears cached cookies and fetches fresh cookies from Firefox.
        /// </summary>
        /// <param name="order">The BestOfferOrder object that needs fresh cookies</param>
        /// <returns>True if cookie refresh was successful, false otherwise</returns>
        private static async Task<bool> RefreshCookiesAsync(BuyingService.BestOfferOrder order)
        {
            try
            {
                // Clear cached cookies to force fresh fetch from Firefox
                Cookies.CookieManager.ClearCookieCache();

                // Re-read cookies from Firefox for the eBay domain
                var domains = new[] { $".{order.EbaySite.Domain}" };

                // Ensure cookies are available before proceeding
                if (!await Cookies.CookieManager.EnsureCookiesAvailableAsync(domains).ConfigureAwait(false))
                {
                    order.FailureReasonMessage = $"Failed to refresh cookies from Firefox profile. [{order.Title}]";
                    return false;
                }

                // Get fresh cookies and update the order
                order.CookieContainer = await Cookies.CookieManager.GetCookiesAsync(domains).ConfigureAwait(false);

                if (order.CookieContainer == null || order.CookieContainer.Count == 0)
                {
                    order.FailureReasonMessage = $"No cookies available after refresh from Firefox profile. [{order.Title}]";
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                order.FailureReasonMessage = $"Exception during cookie refresh: {ex.Message} [{order.Title}]";
                return false;
            }
        }

        /// <summary>
        /// Step 1: Loads the initial "Make Offer" page to gather necessary tokens and context.
        /// Corresponds to Log 1.
        /// </summary>
        /// <param name="order">The BestOfferOrder object containing item details and CookieContainer.</param>
        /// <returns>True if successful and data extracted, false otherwise.</returns>
        public static async Task<bool> _1_LoadMakeOfferPageAsync(BuyingService.BestOfferOrder order)
        {
            if (order.CookieContainer == null || order.CookieContainer.Count == 0)
            {
                order.FailureReasonMessage = $"No valid cookies found. [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }

            // 1. Construct URL
            var url = $"https://www.{order.EbaySite.Domain}/bo/makeOffer/{order.ItemID}?quantity={order.Quantity}";
            var refererAffiliateUrl = new Uri(AffiliateTool.GetItemAffiliateLink(order.ItemID, order.EbaySite, "MakeOffer"));

            // 2. Define Headers
            var headers = new Dictionary<string, string>
            {
                { "Accept", "text/html" }, // Updated based on log 1
                { "Sec-Fetch-Site", "same-origin" }, // Added based on log 1
                { "Sec-Fetch-Mode", "cors" },        // Added based on log 1
                { "Sec-Fetch-Dest", "empty" }        // Added based on log 1
                // User-Agent is handled by the helper
            };
            // Using simpler referrer for now, as complex one from log might not be necessary/reproducible


            var acceptLanguage = "en-US,en;q=0.5"; // From log 1
            try
            {
                // 3. Call Helper with specific Accept-Language
                var result = await HttpServiceHelper.GetAsync(url, order.CookieContainer, headers, refererAffiliateUrl, acceptLanguage);

                // 4. Check response status from helper result
                if (!result.IsSuccess)
                {
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(result.Content) ?? $"HTTP Error: {result.StatusCode}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 4.5. Check for logout/authentication failure in response content
                if (Restocker.Services.CaptchaCooldownManager.IsLogoutError(result.Content))
                {
                    order.FailureReasonMessage = "User logged out - signin redirect detected";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 5. Use response content
                var jsonContent = result.Content;

                // 6. Extract zipcode from response and assign to order.Zipcode
                order.ShipToLocation = ExtractShipToLocation(jsonContent);

                // 7. Parse HTML to extract srt tokens, form fields, etc.
                order.SrtTokens = new List<string?>();
                try
                {
                    // Parse the entire response content as JSON
                    var jsonDoc = JObject.Parse(jsonContent);
                    var globalData = jsonDoc["$global"];

                    if (globalData is { Type: JTokenType.Object } && globalData["srt"] is JArray srtArray)
                    {
                        order.SrtTokens = srtArray.Select(token => token.Value<string>()).Where(s => s != null).ToList();

                        if (order.SrtTokens.Count == 0)
                        {
                            order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(jsonContent) ?? "Failed to find security tokens (SRT) in response."} [{order.Title}]";
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                            return false;
                        }
                    }
                    else
                    {
                        order.SrtTokens = new List<string?>(); // Ensure it's an empty list, not null
                        order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(jsonContent) ?? "Security token (SRT) data missing or invalid in response."} [{order.Title}]";
                        order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                        return false;
                    }
                }
                catch (JsonException ex)
                {
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(jsonContent) ?? $"JSON Parsing Error: {ex.Message}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }
                catch (Exception ex) // Catch other potential errors
                {
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(jsonContent) ?? $"Error processing offer page: {ex.Message}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 10. Return true
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferPageLoaded;
                return true;
            }
            // HttpRequestException is handled by the helper, returning IsSuccess = false
            catch (JsonException ex) // Keep specific parsing errors
            {
                order.FailureReasonMessage = $"Outer JSON Parsing Error: {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
            catch (Exception ex) // Catch other potential errors during parsing or logic
            {
                order.FailureReasonMessage = $"Unexpected Error (Step 1): {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
        }

        /// <summary>
        /// Step 2: Submits the initial offer details for review.
        /// Corresponds to Log 2.
        /// </summary>
        /// <param name="order">The BestOfferOrder object containing details and cookies.</param>
        /// <returns>True if successful and checkout URL/payload extracted, false otherwise.</returns>
        public static async Task<bool> _2_ReviewOfferAsync(BuyingService.BestOfferOrder order)
        {
            if (order.CookieContainer == null || order.CookieContainer.Count == 0 || order.SrtTokens == null || order.SrtTokens.Count == 0)
            {
                order.FailureReasonMessage = $"Missing cookies or security tokens for review step. [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }

            // 1. Construct URL
            var url = $"https://www.{order.EbaySite.Domain}/bo/reviewOffer/{order.ItemID}?user_role=BUYER&negotiation_type=BEST_OFFER"; // From Log 2

            // 2. Build JSON Payload (Mimic structure from Log 2, line 87)
            var payload = new
            {
                itemId = order.ItemID,
                quantity = order.Quantity,
                offerId = "",
                negotiationType = "BEST_OFFER",
                userRole = "BUYER",
                requestAttributes = new { inputOption = order.ShipToLocation ?? "", customizationToken = "" },
                srt = order.SrtTokens.FirstOrDefault() ?? "",
                offerPrice = new { value = order.OfferTotalPrice.ToString(CultureInfo.InvariantCulture), currency = order.OfferCurrency },
                message = order.OfferMessage
            };


            // 4. Define Headers based on log 2
            var headers = new Dictionary<string, string>
            {
                { "Accept", "*/*" },
                { "Sec-Fetch-Site", "same-origin" },
                { "Sec-Fetch-Mode", "cors" },
                { "Sec-Fetch-Dest", "empty" }
                // User-Agent is handled by the helper
            };
            // Using simpler referrer for now
            var refererAffiliateUrl = new Uri(AffiliateTool.GetItemAffiliateLink(order.ItemID, order.EbaySite, "MakeOffer"));

            var origin = $"https://www.{order.EbaySite.Domain}";
            var acceptLanguage = "en-US,en;q=0.5"; // From log 2

            try
            {
                // 5. Call Helper with specific Accept-Language
                var result = await HttpServiceHelper.PostJsonAsync(url, order.CookieContainer, payload, headers, refererAffiliateUrl, origin, acceptLanguage);

                // 6. Check response status from helper result
                if (!result.IsSuccess)
                {
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(result.Content) ?? $"HTTP Error: {result.StatusCode}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 6.5. Check for logout/authentication failure in response content
                if (Restocker.Services.CaptchaCooldownManager.IsLogoutError(result.Content))
                {
                    order.FailureReasonMessage = "User logged out - signin redirect detected";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 7. Use response content
                var jsonResponse = result.Content;

                // 8. Parse JSON response to determine flow (Short or Long)
                try
                {
                    var jsonDoc = JObject.Parse(jsonResponse);
                    var shortFlowDetected = false;
                    var longFlowDetected = false;

                    // Attempt to parse for Short Flow (SEND_OFFER action)
                    var offerActions = jsonDoc?["model"]?["modules"]?["OFFER_ACTIONS"]?["offerActions"] as JArray;
                    var sendOfferAction = offerActions?.FirstOrDefault(action => action?["action"]?["name"]?.Value<string>() == "SEND_OFFER");
                    var globalData = jsonDoc?["$global"];
                    string? step5SrtFromGlobal = null;

                    if (globalData is { Type: JTokenType.Object } && globalData["srt"] is JArray { Count: > 0 } srtArray)
                    {
                        step5SrtFromGlobal = srtArray[0]?.Value<string>(); // Short flow uses the first token for Step 5
                    }

                    if (sendOfferAction != null && !string.IsNullOrEmpty(step5SrtFromGlobal))
                    {
                        order.SendOfferUrl = sendOfferAction?["action"]?["URL"]?.Value<string>();
                        order.SendOfferPayload = sendOfferAction?["action"]?["params"]?["postBody"]?.Value<string>(); // Payload for Step 5
                        order.Step5SrtToken = step5SrtFromGlobal; // SRT for Step 5

                        if (!string.IsNullOrEmpty(order.SendOfferUrl) && !string.IsNullOrEmpty(order.SendOfferPayload))
                        {
                            shortFlowDetected = true;
                            order.IsShortFlow = true;
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferReviewed; // Ready for Step 5
                        }
                    }

                    // If Short Flow not detected, attempt to parse for Long Flow (screenFlowDestination)
                    if (!shortFlowDetected)
                    {
                        // Logger.Log("Short flow not detected, checking for long flow...");
                        var screenFlowDestination = jsonDoc?["model"]?["meta"]?["screenFlowDestination"];
                        var checkoutUrl = screenFlowDestination?["URL"]?.Value<string>();
                        var postBodyJsonString = screenFlowDestination?["params"]?["postBody"]?.Value<string>();
                        string? step3SrtToken = null; // For Step 3
                        // Step5SrtToken is already extracted above (first token from global)

                        if (globalData is { Type: JTokenType.Object } && globalData["srt"] is JArray { Count: > 1 } srtArrayLong)
                        {
                            // Long flow uses the second token for Step 3
                            step3SrtToken = srtArrayLong[1]?.Value<string>();
                        }
                        else if (globalData is { Type: JTokenType.Object } && globalData["srt"] is JArray { Count: 1 } srtArraySingle)
                        {
                            // If only one token, use it for both Step 3 and Step 5/4
                            step3SrtToken = srtArraySingle[0]?.Value<string>();
                        }


                        if (!string.IsNullOrEmpty(checkoutUrl) && !string.IsNullOrEmpty(postBodyJsonString) && !string.IsNullOrEmpty(step3SrtToken) && !string.IsNullOrEmpty(step5SrtFromGlobal))
                        {
                            order.CheckoutUrl = checkoutUrl; // URL for Step 3
                            order.Step3SrtToken = step3SrtToken; // SRT for Step 3
                            order.Step5SrtToken = step5SrtFromGlobal; // SRT for Step 5 (and potentially Step 4)

                            // Parse the inner postBody for Step 3 parameters
                            try
                            {
                                var postBodyDoc = JObject.Parse(postBodyJsonString);
                                order.FinalOfferAction = postBodyDoc?["action"]?.Value<string>();
                                order.OfferCurrency = postBodyDoc?["offercurrency"]?.Value<string>();
                                order.FinalOfferAmount = postBodyDoc?["offeramount"]?.Value<string>();

                                // Validate Step 3 specific data
                                if (!string.IsNullOrEmpty(order.FinalOfferAction) &&
                                    !string.IsNullOrEmpty(order.FinalOfferAmount))
                                {
                                    longFlowDetected = true;
                                    order.IsShortFlow = false;
                                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferReviewNeeded;
                                }
                            }
                            catch (JsonException)
                            {

                            }
                        }
                    }

                    // If neither flow was successfully detected and parsed
                    if (!shortFlowDetected && !longFlowDetected)
                    {
                        // Logger.Log("Failed to detect or parse data for either short or long flow.");
                        order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(jsonResponse) ?? "Could not determine offer flow (short/long) from response."} [{order.Title}]";
                        order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                        return false;
                    }
                }
                catch (JsonException ex)
                {
                    // Logger.Log($"Error parsing reviewOffer response JSON: {ex.Message}");
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(jsonResponse) ?? $"JSON Parsing Error: {ex.Message}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 10. Return true if either flow was successfully processed
                return true;
            }
            // HttpRequestException is handled by the helper
            catch (JsonException ex) // Keep specific parsing errors
            {
                order.FailureReasonMessage = $"Outer JSON Parsing Error: {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
            catch (Exception ex) // Catch other potential errors during parsing or logic
            {
                order.FailureReasonMessage = $"Unexpected Error (Step 2): {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
        }

        /// <summary>
        /// Step 3: Submits the final offer to the checkout system (Long Flow).
        /// Corresponds to Log 3.
        /// </summary>
        /// <param name="order">The BestOfferOrder object containing details, cookies, checkoutUrl, finalPayload.</param>
        /// <returns>True if submission was successful (based on response), false otherwise.</returns>
        public static async Task<bool> _3_PaymentReviewAsync(BuyingService.BestOfferOrder order) // Renamed method
        {
            // Validate required data for Long Flow Step 3
            if (order.CookieContainer == null || order.CookieContainer.Count == 0 ||
                string.IsNullOrEmpty(order.CheckoutUrl) ||
                string.IsNullOrEmpty(order.FinalOfferAction) ||
                string.IsNullOrEmpty(order.FinalOfferAmount) ||
                string.IsNullOrEmpty(order.Step3SrtToken))
            {
                order.FailureReasonMessage = $"Missing required data for payment review step. [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }

            // 1. Use order.CheckoutUrl
            var url = order.CheckoutUrl + "?ru=" + WebUtility.UrlEncode(AffiliateTool.GetItemAffiliateLink(order.ItemID, order.EbaySite, "MakeOffer"));

            // 2. Build FormUrlEncodedContent payload (Mimic Log 3, line 81)
            var formData = new Dictionary<string, string>
            {
                { "action", order.FinalOfferAction },
                { "item", order.ItemID },
                { "quantity", order.Quantity.ToString() },
                { "srt", order.Step3SrtToken }, // Use the specific SRT token extracted for this step
                { "offeramount", order.FinalOfferAmount },
                { "offercurrency", order.OfferCurrency },
            };

            // 4. Define Headers
            var headers = new Dictionary<string, string>
            {
                { "Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" },
                { "Sec-Fetch-Site", "same-site" },         // Updated based on log 3
                { "Sec-Fetch-Mode", "navigate" },        // Updated based on log 3
                { "Sec-Fetch-Dest", "iframe" },          // Updated based on log 3
                { "Sec-Fetch-User", "?1" },              // Added based on log 3
                { "Cache-Control", "max-age=0" },        // Added based on log 3
                { "Upgrade-Insecure-Requests", "1" }     // Added based on log 3
                // User-Agent is handled by the helper
            };
            // No Referrer needed based on Log 3
            var origin = $"https://www.{order.EbaySite.Domain}";
            var acceptLanguage = "en-US,en;q=0.5"; // From log 3
            var refererAffiliateUrl = new Uri(AffiliateTool.GetItemAffiliateLink(order.ItemID, order.EbaySite, "MakeOffer"));
            try
            {
                // 5. Call Helper with specific Accept-Language
                var result = await HttpServiceHelper.PostFormUrlEncodedAsync(url, order.CookieContainer, formData, headers, refererAffiliateUrl, origin, true, acceptLanguage); // allowAutoRedirect = true

                // 6. Check response status from helper result
                if (!result.IsSuccess)
                {
                    // Step 3 often returns HTML on failure, so ExtractErrorMessageFromJson might not work well.
                    // Prioritize status code, but attempt extraction.
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(result.Content) ?? $"HTTP Error: {result.StatusCode}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 6.5. Check for logout/authentication failure in response content
                if (Restocker.Services.CaptchaCooldownManager.IsLogoutError(result.Content))
                {
                    order.FailureReasonMessage = "User logged out - signin redirect detected";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 7. Use response content
                var htmlContent = result.Content;

                try
                {
                    // 1. Extract the __APP_INITIAL_STATE__ JSON block
                    var initialStateRegex = new Regex(
                        @"window\.__APP_INITIAL_STATE__\s*=\s*(\{.*?\})\s*<\/script>",
                        RegexOptions.Multiline
                    );
                    var initialStateMatch = initialStateRegex.Match(htmlContent);

                    if (initialStateMatch is { Success: true, Groups.Count: > 1 })
                    {
                        var initialStateJsonString = initialStateMatch.Groups[1].Value;
                        var initialState = JObject.Parse(initialStateJsonString);

                        // 2. Extract SessionID from the parsed JSON
                        order.SessionID = initialState?["sessionData"]?["meta"]?["__xo"]?["sessionId"]?.Value<string>() ?? "";
                        order.Step4SrtToken = initialState?["context"]?["csrf"]?.Value<string>();
                        // 3. Extract other parameters needed for Step 4 from the parsed JSON
                        var sessionCallToActionParams = initialState?["sessionData"]?["modules"]?["sessionCallToAction"]?["action"]?["params"];
                        if (sessionCallToActionParams != null)
                        {
                            order.PaymentInstrumentId = sessionCallToActionParams["paymentinstrumentid"]?.Value<string>();
                            order.PaymentInstrumentType = sessionCallToActionParams["paymentinstrumenttype"]?.Value<string>();
                            order.AdditionalParametersForSubmit = sessionCallToActionParams["additionalparametersforsubmit"]?.Value<string>();
                            order.DisableSca = sessionCallToActionParams["disableSCA"]?.Value<string>();
                            order.IsPurchaseEmbeddedInScaAction = sessionCallToActionParams["isPurchaseEmbeddedInSCAAction"]?.Value<string>();
                            order.AddressID = sessionCallToActionParams["addressid"]?.Value<string>();
                            order.LogisticsId = sessionCallToActionParams["logisticsid"]?.Value<string>();
                        }
                    }
                    else
                    {
                        // This is likely fatal if SessionID is needed later
                        order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(htmlContent) ?? "Failed to extract session state from payment review page."} [{order.Title}]";
                        order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                        return false;
                    }
                }
                catch (JsonException ex)
                {
                    // Decide if this is fatal
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(htmlContent) ?? $"JSON Parsing Error: {ex.Message}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }
                catch (RegexMatchTimeoutException ex)
                {
                    // Decide if this is fatal
                    order.FailureReasonMessage = $"Regex Timeout Error: {ex.Message} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }
                catch (Exception ex) // Catch other potential errors during parsing
                {
                    // Decide if this is fatal
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(htmlContent) ?? $"Error processing payment review page: {ex.Message}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // If SessionID is still missing after attempting extraction, consider it a failure
                // Validate SessionID specifically, as it's critical for the next step
                if (string.IsNullOrEmpty(order.SessionID))
                {
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(htmlContent) ?? "Failed to extract SessionID after payment review."} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 8. Return true
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferSubmitting; // Or a state indicating this step is done
                return true;
            }
            // HttpRequestException is handled by the helper
            catch (JsonException ex) // Keep specific parsing errors
            {
                order.FailureReasonMessage = $"Outer JSON Parsing Error: {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
            catch (RegexMatchTimeoutException ex) // Keep specific parsing errors
            {
                order.FailureReasonMessage = $"Outer Regex Timeout Error: {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
            catch (Exception ex) // Catch other potential errors during parsing or logic
            {
                order.FailureReasonMessage = $"Unexpected Error (Step 3): {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
        }

        /// <summary>
        /// Step 4: Initializes authentication/state after final submission.
        /// Corresponds to Log 4.
        /// </summary>
        /// <param name="order">The BestOfferOrder object containing details and cookies.</param>
        /// <returns>True if successful and pseudoOrderId extracted, false otherwise.</returns>
        public static async Task<bool> _4_InitAuthAsync(BuyingService.BestOfferOrder order)
        {
            if (order.CookieContainer == null || order.CookieContainer.Count == 0)
            {
                order.FailureReasonMessage = $"No valid cookies found for auth step. [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
            // Validate data needed for payload (SRT token and SessionID are crucial here)
            if (string.IsNullOrEmpty(order.Step4SrtToken) || string.IsNullOrEmpty(order.SessionID)) // Corrected token check
            {
                order.FailureReasonMessage = $"Missing security token or SessionID for auth step. [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }

            // 1. Construct URL
            var url = $"https://checkout.{order.EbaySite.Domain}/ajax/m/bo?action=initAuth"; // Domain might vary based on site
            var refererAffiliateUrl = new Uri("https://checkout.ebay.com/int/m/bo?ru=" + WebUtility.UrlEncode(AffiliateTool.GetItemAffiliateLink(order.ItemID, order.EbaySite, "MakeOffer")));
            // 2. Build JSON Payload

            var payload = new
            {
                forterRiskToken = $"{Guid.NewGuid().ToString("N")}_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}__{new string(Enumerable.Range(0, 12).Select(_ => "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".ToCharArray()[new Random().Next(0, 64)]).ToArray())}_", //stored in browser in local storage
                marketingoptin = false,
                item = order.ItemID,
                paymentinstrumentid = order.PaymentInstrumentId,
                currency = order.OfferCurrency,
                paymentinstrumenttype = order.PaymentInstrumentType,
                additionalparametersforsubmit = order.AdditionalParametersForSubmit,
                logisticsid = order.LogisticsId,
                pum = (object?)null,
                addressid = order.AddressID,
                disableSCA = order.DisableSca,
                isPurchaseEmbeddedInSCAAction = order.IsPurchaseEmbeddedInScaAction,
                authenticationData = new
                {
                    notificationURL = $"https://checkout.{order.EbaySite.Domain}/sca/chs/{order.SessionID}",
                    channel = "WEB",
                    acceptHeader = "text/html",
                    colorDepth = 24,
                    javaEnabled = false,
                    localeLanguage = "en-US",
                    screenHeight = 1080,
                    screenWidth = 1920,
                    timeZoneOffset = -120,
                    userAgent = ProgramState.ChromeUA
                },
                sessionid = order.SessionID,
                srt = order.Step4SrtToken
            };

            // 4. Define Headers based on log 4
            var headers = new Dictionary<string, string>
            {
                { "Accept", "*/*" },
                { "Sec-Fetch-Site", "same-origin" }, // Added based on log 4
                { "Sec-Fetch-Mode", "cors" },        // Added based on log 4
                { "Sec-Fetch-Dest", "empty" }        // Added based on log 4
                // User-Agent is handled by the helper
            };

            var origin = $"https://checkout.{order.EbaySite.Domain}";
            var acceptLanguage = "en-US,en;q=0.5"; // From log 4

            try
            {
                // 5. Call Helper with specific Accept-Language and Referrer
                var result = await HttpServiceHelper.PostJsonAsync(url, order.CookieContainer, payload, headers, refererAffiliateUrl, origin, acceptLanguage);

                // 6. Check response status from helper result
                if (!result.IsSuccess)
                {
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(result.Content) ?? $"HTTP Error: {result.StatusCode}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 6.5. Check for logout/authentication failure in response content
                if (Restocker.Services.CaptchaCooldownManager.IsLogoutError(result.Content))
                {
                    order.FailureReasonMessage = "User logged out - signin redirect detected";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 7. Use response content
                var jsonResponse = result.Content;

                // 8. Parse JSON to extract pseudoOrderId (or orderID)
                try
                {
                    var jsonDoc = JObject.Parse(jsonResponse);
                    // Path from Log 4 response: modules.orderInfo.orderID
                    var orderIdElement = jsonDoc?["meta"]?["__xo"]?["orderInfo"]?["orderID"];

                    if (orderIdElement != null)
                    {
                        order.PseudoOrderId = orderIdElement.Value<string>();
                    }

                    // 9. Store pseudoOrderId (done above)

                    if (string.IsNullOrEmpty(order.PseudoOrderId))
                    {
                        order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(jsonResponse) ?? "Failed to extract Order ID after auth step."} [{order.Title}]";
                        order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                        return false;
                    }
                }
                catch (JsonException ex)
                {
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(jsonResponse) ?? $"JSON Parsing Error: {ex.Message}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 10. Return true (even if PseudoOrderId wasn't found, allow next step to proceed for now)
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferAuthInitializing;
                return true;
            }
            // HttpRequestException is handled by the helper
            catch (JsonException ex) // Keep specific parsing errors
            {
                order.FailureReasonMessage = $"Outer JSON Parsing Error: {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
            catch (Exception ex) // Catch other potential errors during parsing or logic
            {
                order.FailureReasonMessage = $"Unexpected Error (Step 4): {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
        }

        /// <summary>
        /// Step 5: Sends the final confirmation back to www.ebay.com.
        /// Corresponds to Log 5.
        /// </summary>
        /// <param name="order">The BestOfferOrder object containing details, cookies, pseudoOrderId.</param>
        /// <returns>True if confirmation was successful, false otherwise.</returns>
        public static async Task<bool> _5_SendOfferAsync(BuyingService.BestOfferOrder order)
        {
            if (order.CookieContainer == null || order.CookieContainer.Count == 0)
            {
                order.FailureReasonMessage = $"No valid cookies found for final send step. [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }

            // --- Conditional Logic Start ---
            string url;
            object payload;
            var refererAffiliateUrl = new Uri(AffiliateTool.GetItemAffiliateLink(order.ItemID, order.EbaySite, "MakeOffer"));

            if (order.IsShortFlow)
            {
                // Validate data for Short Flow
                if (string.IsNullOrEmpty(order.SendOfferUrl) || string.IsNullOrEmpty(order.Step5SrtToken))
                {
                    // Logger.Log($"Missing data for final confirmation (Short Flow): URL={!string.IsNullOrEmpty(order.SendOfferUrl)}, SRT={!string.IsNullOrEmpty(order.Step5SrtToken)}");
                    order.FailureReasonMessage = $"Missing URL or security token for final send step (Short Flow). [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 1. Use URL from Step 2
                url = order.SendOfferUrl;

                // 2. Build JSON Payload for Short Flow (matches Log 5)
                payload = new
                {
                    itemId = order.ItemID,
                    offerPrice = new { value = order.OfferTotalPrice.ToString(), currency = order.OfferCurrency },
                    quantity = order.Quantity,
                    message = order.OfferMessage,
                    negotiationType = "BEST_OFFER",
                    userRole = "BUYER",
                    requestAttributes = new { inputOption = order.ShipToLocation ?? "", customizationToken = "" },
                    srt = order.Step5SrtToken
                };
            }
            else // Long Flow
            {
                // Validate data for Long Flow
                if (string.IsNullOrEmpty(order.Step5SrtToken) || // Step 5 SRT is still needed
                    string.IsNullOrEmpty(order.FinalOfferAction) ||
                    string.IsNullOrEmpty(order.OfferCurrency) ||
                    string.IsNullOrEmpty(order.FinalOfferAmount))
                {
                    // Logger.Log($"Missing data for final confirmation (Long Flow): SRT={!string.IsNullOrEmpty(order.Step5SrtToken)}, Action={!string.IsNullOrEmpty(order.FinalOfferAction)}, Currency={!string.IsNullOrEmpty(order.OfferCurrency)}, Amount={!string.IsNullOrEmpty(order.FinalOfferAmount)}");
                    order.FailureReasonMessage = $"Missing required data for final send step (Long Flow). [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 1. Construct URL manually for Long Flow's final step
                url = $"https://www.{order.EbaySite.Domain}/bo/sendOffer/{order.ItemID}?user_role=BUYER&negotiation_type=BEST_OFFER";

                // 2. Build JSON Payload for Long Flow (Original Step 5 structure)
                payload = new
                {
                    item = order.ItemID, // Note: 'item' vs 'itemId'
                    quantity = order.Quantity,
                    offercurrency = order.OfferCurrency, // Note: 'offercurrency' vs 'offerPrice.currency'
                    action = order.FinalOfferAction,
                    offeramount = order.FinalOfferAmount, // Note: 'offeramount' vs 'offerPrice.value'
                    srt = order.Step5SrtToken,
                    offerPrice = new { value = order.OfferTotalPrice.ToString("F2"), currency = order.OfferCurrency }, // Keep original formatting? Check if needed.
                    message = order.OfferMessage,
                    pseudoOrderId = order.PseudoOrderId ?? "" // Include if available from Step 4
                };
            }
            // --- Conditional Logic End ---


            // 4. Define Headers (seem consistent for both flows based on logs)
            var headers = new Dictionary<string, string>
            {
                { "Accept", "*/*" },
                { "Sec-Fetch-Site", "same-origin" },
                { "Sec-Fetch-Mode", "cors" },
                { "Sec-Fetch-Dest", "empty" }
                // User-Agent is handled by the helper
            };
            // Referer is passed to helper
            var origin = $"https://www.{order.EbaySite.Domain}";
            var acceptLanguage = "en-US,en;q=0.9,uk;q=0.8,ru;q=0.7"; // Updated from log 5

            try
            {
                // 5. Call Helper with specific Accept-Language
                // NOTE: The HttpServiceHelper class name might be HttpServiceHelper1 based on previous steps
                var result = await HttpServiceHelper.PostJsonAsync(url, order.CookieContainer, payload, headers, refererAffiliateUrl, origin, acceptLanguage);

                // 6. Check response status from helper result
                if (!result.IsSuccess)
                {
                    order.FailureReasonMessage = $"{ExtractErrorMessageFromJson(result.Content) ?? $"HTTP Error: {result.StatusCode}"} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    return false;
                }

                // 7. Use response content
                var jsonResponse = result.Content;

                // 8. Parse response to determine actual offer outcome
                var (outcome, message, offersRemaining, retryUrl) = AnalyzeOfferResponse(jsonResponse);

                // Store additional information in the order object (you may need to add these properties)
                // order.OffersRemaining = offersRemaining;
                // order.RetryUrl = retryUrl;

                var confirmedSuccess = false;
                try
                {
                    switch (outcome)
                    {
                        case OfferOutcome.OfferSent:
                            confirmedSuccess = true;
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferSent;
                            break;
                        case OfferOutcome.OfferAccepted:
                            confirmedSuccess = true;
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferAccepted; // You may need to add this status
                            break;
                        case OfferOutcome.OfferDeclined:
                            confirmedSuccess = false;
                            order.FailureReasonMessage = $"{message} (Offers remaining: {offersRemaining ?? 0}) [{order.Title}]";
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferDeclined; // You may need to add this status
                            break;
                        case OfferOutcome.OfferCountered:
                            confirmedSuccess = false; // Or true, depending on how you want to handle counter offers
                            order.FailureReasonMessage = $"{message} [{order.Title}]";
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferCountered; // You may need to add this status
                            break;
                        case OfferOutcome.OfferExpired:
                            confirmedSuccess = false;
                            order.FailureReasonMessage = $"{message} [{order.Title}]";
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferExpired; // You may need to add this status
                            break;
                        case OfferOutcome.OfferPending:
                            confirmedSuccess = false;
                            order.FailureReasonMessage = $"{message} [{order.Title}]";
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferPending;
                            break;
                        default:
                            confirmedSuccess = false;
                            order.FailureReasonMessage = $"{message ?? "Unknown offer outcome"} [{order.Title}]";
                            order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                            break;
                    }
                }
                catch (JsonException ex)
                {
                    confirmedSuccess = false;
                    order.FailureReasonMessage = $"JSON Parsing Error on confirmation: {ex.Message} [{order.Title}]";
                    order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                }


                // 9. Return true/false based on offer outcome
                // Note: You might want to return true for declined offers too if you want to handle retries elsewhere
                if (confirmedSuccess)
                {
                    // Status already set in the switch statement above
                    return true;
                }
                else
                {
                    // Failure reason and status already set in the switch statement above
                    return false;
                }
            }
            // HttpRequestException is handled by the helper
            catch (JsonException ex) // Keep specific parsing errors
            {
                // confirmedSuccess = false; // Treat parsing error as failure?
                order.FailureReasonMessage = $"Outer JSON Parsing Error: {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed; // Assume parsing failure is fatal here
                return false;
            }
            catch (Exception ex) // Catch other potential errors during parsing or logic
            {
                order.FailureReasonMessage = $"Unexpected Error (Step 5): {ex.Message} [{order.Title}]";
                order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                return false;
            }
        }
    }
}
