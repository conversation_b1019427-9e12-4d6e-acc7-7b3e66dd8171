﻿using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Threading.Tasks;
using DevExpress.LookAndFeel;
using DevExpress.Skins;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Helpers;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst.Data;

namespace uBuyFirst.Filters
{
    static class FormatRuleManager
    {
        /// <summary>
        /// Synchronous version - ONLY safe for non-UI threads
        /// DO NOT call from UI thread - use ApplyGridFormatRulesAsync instead to avoid deadlocks
        /// </summary>
        public static void ApplyGridFormatRules(GridView grView)
        {
            if (grView == null)
                return;

            grView.BeginUpdate();
            ApplyGridFormatRulesNoLock(grView);
            grView.EndUpdate();
        }

        private static void ApplyGridFormatRulesNoLock(GridView grView)
        {
            grView.FormatRules.Clear();

            var defaultFormatRules = CreateDefaultFormatRules(grView);
            grView.FormatRules.AddRange(defaultFormatRules);

            var dataTable = (DataTable)grView.GridControl.DataSource;
            XFilterManager.ApplyFilters(grView, dataTable);
        }

        private static List<GridFormatRule> CreateDefaultFormatRules(GridView grView)
        {
            Skin currentSkin = CommonSkins.GetSkin(grView.GridControl.LookAndFeel);
            var foreColor = currentSkin.TranslateColor(grView.PaintAppearance.Row.ForeColor);
            var green = Color.FromArgb(240, 255, 240);
            var greenUpdated = Color.FromArgb(240, 255, 190);
            var lightSalmon = Color.LightSalmon;
            var mediumOrchid = Color.FromArgb(255, 193, 7); // Orange color matching Test Mode dropdown
            var yellow = Color.Yellow;
            var yellowDark = Color.Gold;
            var bestOfferInProgressColor = Color.LightSkyBlue;
            var bestOfferSubmittedColor = Color.DodgerBlue;

            if (foreColor.B > 150)
            {
                green = Color.DarkGreen;
                greenUpdated = Color.DarkOliveGreen;
                mediumOrchid = Color.FromArgb(204, 154, 5); // Darker orange for dark theme visibility
                yellow = Color.Olive;
            }

            var ruleActive = new FormatConditionRuleExpression();
            ruleActive.Expression = $"([Status] = '{ItemStatus.Active}')";
            ruleActive.Appearance.BackColor = green;

            var ruleUpdated = new FormatConditionRuleExpression();
            ruleUpdated.Expression = $"([Status] = '{ItemStatus.Updated}')";
            ruleUpdated.Appearance.BackColor = greenUpdated;

            var ruleSold = new FormatConditionRuleExpression();
            ruleSold.Expression = $"([Status] = '{ItemStatus.Sold}')";
            ruleSold.Appearance.BackColor = lightSalmon;

            var ruleTestPurchase = new FormatConditionRuleExpression();
            ruleTestPurchase.Expression = $"([Status] = '{ItemStatus.TestPurchase}')";
            ruleTestPurchase.Appearance.BackColor = mediumOrchid;

            var ruleCheckout = new FormatConditionRuleExpression();
            ruleCheckout.Expression = $"([Status] = '{ItemStatus.CreatingSession}')";
            ruleCheckout.Appearance.BackColor = yellow;

            var rulePayment = new FormatConditionRuleExpression();
            rulePayment.Expression = $"([Status] = '{ItemStatus.PaymentInProgress}')";
            rulePayment.Appearance.BackColor = yellowDark;

            var ruleOfferInProgress = new FormatConditionRuleExpression();
            ruleOfferInProgress.Expression = $"([Status] = '{ItemStatus.BestOfferInProgress}')";
            ruleOfferInProgress.Appearance.BackColor = bestOfferInProgressColor;

            var ruleOfferSubmitted = new FormatConditionRuleExpression();
            ruleOfferSubmitted.Expression = $"([Status] = '{ItemStatus.BestOfferSubmitted}')";
            ruleOfferSubmitted.Appearance.BackColor = bestOfferSubmittedColor;

            var ruleCheckoutFailed = new FormatConditionRuleExpression();
            ruleCheckoutFailed.Expression = $"([Status] = '{ItemStatus.CheckoutFailed}')";
            ruleCheckoutFailed.Appearance.BackColor = Color.MistyRose; // Light red to indicate failure

            var gridFormatRule1 = new GridFormatRule();
            gridFormatRule1.ApplyToRow = true;
            gridFormatRule1.Rule = ruleActive;
            gridFormatRule1.Column = grView.GetVisibleColumn(0);

            var gridFormatRule2 = new GridFormatRule();
            gridFormatRule2.ApplyToRow = true;
            gridFormatRule2.Rule = ruleSold;
            gridFormatRule2.Column = grView.GetVisibleColumn(0);

            var gridFormatRule3 = new GridFormatRule();
            gridFormatRule3.ApplyToRow = true;
            gridFormatRule3.Rule = ruleUpdated;
            gridFormatRule3.ColumnApplyToName = "Auction Price";

            var gridFormatRule4 = new GridFormatRule();
            gridFormatRule4.ApplyToRow = true;
            gridFormatRule4.Rule = ruleCheckout;
            gridFormatRule4.Column = grView.GetVisibleColumn(0);

            var gridFormatRule5 = new GridFormatRule();
            gridFormatRule5.ApplyToRow = true;
            gridFormatRule5.Rule = rulePayment;
            gridFormatRule5.Column = grView.GetVisibleColumn(0);

            var gridFormatRule6 = new GridFormatRule();
            gridFormatRule6.ApplyToRow = true;
            gridFormatRule6.Rule = ruleOfferInProgress;
            gridFormatRule6.Column = grView.GetVisibleColumn(0);

            var gridFormatRule7 = new GridFormatRule();
            gridFormatRule7.ApplyToRow = true;
            gridFormatRule7.Rule = ruleOfferSubmitted;
            gridFormatRule7.Column = grView.GetVisibleColumn(0);

            var gridFormatRule8 = new GridFormatRule();
            gridFormatRule8.ApplyToRow = true;
            gridFormatRule8.Rule = ruleTestPurchase;
            gridFormatRule8.Column = grView.GetVisibleColumn(0);

            var gridFormatRule9 = new GridFormatRule();
            gridFormatRule9.ApplyToRow = true;
            gridFormatRule9.Rule = ruleCheckoutFailed;
            gridFormatRule9.Column = grView.GetVisibleColumn(0);

            var defaultFormatRules = new List<GridFormatRule>
            {
                gridFormatRule1,
                gridFormatRule2,
                gridFormatRule3,
                gridFormatRule4,
                gridFormatRule5,
                gridFormatRule6,
                gridFormatRule7,
                gridFormatRule8,
                gridFormatRule9
            };
            defaultFormatRules.AddRange(CreateTempHighLightRules(grView));
            return defaultFormatRules;
        }

        private static IEnumerable<GridFormatRule> CreateTempHighLightRules(GridView gridView)
        {
            GridFormatRule rule1 = new GridFormatRule();
            rule1.Rule = GetFormatCondition();
            rule1.Column = gridView.Columns["Shipping"];
            rule1.Name = "Shipping";

            GridFormatRule rule2 = new GridFormatRule();
            rule2.Rule = GetFormatCondition();
            rule2.Column = gridView.Columns["Item Price"];
            rule2.ApplyToRow = true;
            rule2.Name = "Item Price";

            GridFormatRule rule3 = new GridFormatRule();
            rule3.Rule = GetFormatCondition();
            rule3.Column = gridView.Columns["Total Price"];
            rule3.Name = "Total Price";

            GridFormatRule rule4 = new GridFormatRule();
            rule4.Rule = GetFormatCondition();
            rule4.Column = gridView.Columns["Quantity"];
            rule4.ApplyToRow = true;
            rule4.Name = "Quantity";

            var rules = new List<GridFormatRule>();
            //rules.Add(rule1);
            rules.Add(rule2);
            //rules.Add(rule3);
            rules.Add(rule4);
            return rules;
        }

        private static FormatConditionRuleDataUpdate GetFormatCondition()
        {
            var app = FormatPredefinedAppearances.Default.Find(UserLookAndFeel.Default, "Green Fill");
            var formatCondition = new FormatConditionRuleDataUpdate();
            formatCondition.HighlightTime = 15000;
            formatCondition.Trigger = FormatConditionDataUpdateTrigger.Custom;
            formatCondition.Appearance.Options.UseFont = false;
            formatCondition.Appearance.BackColor = app.Appearance.BackColor;
            return formatCondition;
        }
    }
}
