using System;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Prefs;
using uBuyFirst.Purchasing.Cookies; // Assuming CookieManager is here
using uBuyFirst.Stats;
using uBuyFirst.Pricing;
using uBuyFirst.Tools;

namespace uBuyFirst.Purchasing
{
    public static class BestOfferCheckout
    {
        /// <summary>
        /// Orchestrates the Best Offer submission process using HTTP emulation and cookies.
        /// </summary>
        /// <param name="d">The DataList item for the offer.</param>
        /// <param name="quantity">The quantity being offered.</param>
        /// <param name="offerTotalPrice">The price being offered.</param>
        /// <param name="itemPriceCurrency"></param>
        /// <param name="message">The optional message to the seller.</param>
        public static async Task ExecuteBestOfferSubmissionAsync(DataList d, int quantity, double offerTotalPrice, string offerCurrency, string? message)
        {
            FlyoutPanelSnackBar flyoutSnackBar = null;

            // Ensure UI operations happen on the UI thread
            if (d.GridControl != null)
            {
                d.GridControl.InvokeIfRequired(() =>
                {
                    try
                    {
                        // Check if control is still valid before creating FlyoutPanelSnackBar
                        if (d.GridControl != null && !d.GridControl.IsDisposed && d.GridControl.IsHandleCreated)
                        {
                            flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error creating FlyoutPanelSnackBar in BestOfferCheckout: {ex.Message}");
                    }
                });
            }

            // Show initial progress message if flyoutSnackBar was successfully created
            if (flyoutSnackBar != null && d.GridControl != null)
            {
                d.GridControl.InvokeIfRequired(() =>
                {
                    try
                    {
                        if (flyoutSnackBar != null && d.GridControl != null && !d.GridControl.IsDisposed && d.GridControl.IsHandleCreated)
                        {
                            flyoutSnackBar.ShowInformation(d.GridControl, "Best offer submission in progress");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error showing initial progress message: {ex.Message}");
                    }
                });
            }
            try
            {
                // Create a BestOfferOrder with the appropriate details
                var effectivePurchasePrice = d.ItemPricing.GetEffectivePurchasePrice();
                var bestOfferOrder = new BuyingService.BestOfferOrder(
                    d.ItemID,
                    d.Title,
                    d.EBaySite,
                    quantity,
                    effectivePurchasePrice,
                    offerTotalPrice,
                    offerCurrency,
                    message ?? string.Empty);
                bestOfferOrder.PurchaseProgress = new Progress<string>(msg =>
                {
                    if (flyoutSnackBar != null && d.GridControl != null)
                    {
                        d.GridControl.InvokeIfRequired(() =>
                        {
                            try
                            {
                                if (flyoutSnackBar != null && d.GridControl != null && !d.GridControl.IsDisposed && d.GridControl.IsHandleCreated)
                                {
                                    flyoutSnackBar.ShowInformation(d.GridControl, msg);
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Error showing progress message: {ex.Message}");
                            }
                        });
                    }
                });

                // Set the order in DataList which will also set BestOfferOrder property
                d.Order = bestOfferOrder;
                d.SetStatus(ItemStatus.BestOfferInProgress); // Indicate process starting

                // 2. Read Cookies
                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies started for Best Offer");

                // Ensure cookies are available before proceeding with best offer
                var domains = new[] { $".{d.Order.EbaySite.Domain}" };
                if (!await CookieManager.EnsureCookiesAvailableAsync(domains).ConfigureAwait(false))
                {
                    d.Order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    d.SetStatus(ItemStatus.Incorrect);
                    PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies failed for Best Offer - no cookies available");
                    return;
                }

                d.Order.CookieContainer = await CookieManager.GetCookiesAsync(domains).ConfigureAwait(false);
                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies stopped for Best Offer");

                // 3. Call BestOfferService methods sequentially
                bool success;

                success = await BestOfferService._1_LoadMakeOfferPageAsync((BuyingService.BestOfferOrder)d.Order);
                if (!success) { HandleOfferFailure(d, "LoadMakeOfferPage"); return; }

                success = await BestOfferService._2_ReviewOfferAsync((BuyingService.BestOfferOrder)d.Order);
                if (!success) { HandleOfferFailure(d, "ReviewOffer"); return; }

                // --- Conditional Flow Logic ---
                var order = (BuyingService.BestOfferOrder)d.Order; // Cast once for convenience
                if (order.IsShortFlow)
                {
                    // Short Flow: Skip steps 3 & 4
                    PaymentLogger.LogPaymentToFile($"[{order.ItemID}] Short Best Offer flow detected. Proceeding to SendOffer.");
                    success = await BestOfferService._5_SendOfferAsync(order);
                    if (!success) { HandleOfferFailure(d, "SendOffer (Short Flow)"); return; }
                }
                else // Long Flow
                {
                    PaymentLogger.LogPaymentToFile($"[{order.ItemID}] Long Best Offer flow detected. Proceeding to SubmitFinalOffer.");
                    // Step 3: Submit Final Offer (Corrected method name)
                    success = await BestOfferService._3_PaymentReviewAsync(order);
                    if (!success) { HandleOfferFailure(d, "SubmitFinalOffer (Long Flow)"); return; }

                    // Step 4: Initialize Auth
                    success = await BestOfferService._4_InitAuthAsync(order);
                    if (!success) { HandleOfferFailure(d, "InitializeAuth (Long Flow)"); return; }

                    // Step 5: Send Offer Confirmation
                    success = await BestOfferService._5_SendOfferAsync(order);
                    if (!success) { HandleOfferFailure(d, "SendOffer (Long Flow)"); return; }
                }
                // --- End Conditional Flow Logic ---

                // 4. Final Status Update on Success (Only if the entire flow succeeded)
                d.Order.CurrentOfferStatus = BuyingService.OfferStatus.OfferSent;

                SafeSetStatus(d, ItemStatus.BestOfferSubmitted);
                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Best Offer submitted successfully (Flow: {(order.IsShortFlow ? "Short" : "Long")}).");
                // Show success message if flyoutSnackBar was successfully created
                if (flyoutSnackBar != null && d.GridControl != null)
                {
                    d.GridControl.InvokeIfRequired(() =>
                    {
                        try
                        {
                            if (flyoutSnackBar != null && d.GridControl != null && !d.GridControl.IsDisposed && d.GridControl.IsHandleCreated)
                            {
                                flyoutSnackBar.ShowSuccess(d.GridControl, $"Best offer submitted successfully [{d.Title}]");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error showing success message: {ex.Message}");
                        }
                    });
                }
                var offerAmountUSD = CurrencyConverter.ConvertToUSD(offerTotalPrice, offerCurrency);
                Pixel.Track(Pixel.EventType.MakeOfferWeb, d.ItemID, offerAmountUSD);
            }
            catch (Exception ex)
            {
                PaymentLogger.LogPaymentToFile($"[{d.ItemID}] Exception during Best Offer submission: {ex}");
                HandleOfferFailure(d, "Exception");
            }
        }

        /// <summary>
        /// Thread-safe method to set DataList status using UI synchronization context
        /// </summary>
        private static void SafeSetStatus(DataList dataList, ItemStatus status)
        {
            if (Form1.Instance?.InvokeRequired == true)
            {
                Form1.Instance.Invoke(() => dataList.SetStatus(status));
            }
            else
            {
                dataList.SetStatus(status);
            }
        }

        private static void HandleOfferFailure(DataList d, string stepName)
        {
            var failureReason = "Unknown error"; // Default message

            if (d.Order is BuyingService.BestOfferOrder bestOfferOrder) // Check if order exists and is the correct type
            {
                bestOfferOrder.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed; // Set status here
                if (!string.IsNullOrWhiteSpace(bestOfferOrder.FailureReasonMessage))
                {
                    failureReason = bestOfferOrder.FailureReasonMessage;
                }
            }

            var fullMessage = $"{failureReason}";
            var displayMessage = $"{fullMessage} [{d.Title}]";

            if (d.GridControl != null)
            {
                // Ensure UI operations happen on the UI thread
                d.GridControl.InvokeIfRequired(() =>
                {
                    try
                    {
                        // Check if control is still valid before creating FlyoutPanelSnackBar
                        if (d.GridControl != null && !d.GridControl.IsDisposed && d.GridControl.IsHandleCreated)
                        {
                            var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                            flyoutSnackBar.ShowFailure(d.GridControl, displayMessage);
                        }
                        else
                        {
                            // Fallback to alert notification if GridControl is invalid
                            ShowAlertNotification("Best Offer Failed", displayMessage);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the exception and fallback to alert notification
                        System.Diagnostics.Debug.WriteLine($"Error showing FlyoutPanelSnackBar in HandleOfferFailure: {ex.Message}");
                        ShowAlertNotification("Best Offer Failed", displayMessage);
                    }
                });
            }
            else
            {
                // Show alert notification when GridControl is not available (e.g., in restock context)
                ShowAlertNotification("Best Offer Failed", displayMessage);
            }

            PaymentLogger.LogPaymentToFile($"[{d.ItemID}] {fullMessage}");
            // d.Order status is set above if order exists
            // Use CheckoutFailed for Live mode, Incorrect for other modes
            var failureStatus = UserSettings.RestockMode == RestockModeEnum.LiveMode
                ? ItemStatus.CheckoutFailed
                : ItemStatus.Incorrect;

            SafeSetStatus(d, failureStatus);
        }

        private static void ShowAlertNotification(string alertCaption, string alertText)
        {
            try
            {
                // Check if running on Windows 7
                var isWindows7 = Environment.OSVersion.Version.Major == 6 && Environment.OSVersion.Version.Minor < 2 || Environment.OSVersion.Version.Major < 6;
                if (isWindows7)
                {
                    var alert = new DevExpress.XtraBars.Alerter.AlertControl();
                    alert.AllowHtmlText = true;
                    alert.AutoHeight = true;
                    alert.FormShowingEffect = DevExpress.XtraBars.Alerter.AlertFormShowingEffect.SlideHorizontal;
                    alert.ShowPinButton = true;

                    // Get the main form as the owner
                    var mainForm = Form1.Instance;
                    if (mainForm != null)
                    {
                        alert.Show(mainForm, alertCaption, alertText);
                    }
                }
                else
                {
                    // For non-Windows 7, just log the message
                    PaymentLogger.LogPaymentToFile($"[Alert] {alertCaption}: {alertText}");
                }
            }
            catch (Exception ex)
            {
                // Fallback to logging if alert fails
                PaymentLogger.LogPaymentToFile($"[Alert Failed] {alertCaption}: {alertText}. Error: {ex.Message}");
            }
        }
    }
}
