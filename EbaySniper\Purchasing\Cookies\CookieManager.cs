﻿using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using DevExpress.XtraEditors;
using Newtonsoft.Json;
using uBuyFirst.Prefs;

namespace uBuyFirst.Purchasing.Cookies
{
    public static class CookieManager
    {
        private static CookieContainer CachedCookies { get; set; }
        public static CookieProfile? Profile { get; set; }

        // Thread-safe cookie storage with expiration tracking
        private static readonly ConcurrentDictionary<string, CookieContainer> _domainCookieCache = new();
        private static readonly ConcurrentDictionary<string, DateTime> _cookieExpirationTimes = new();
        private static readonly SemaphoreSlim _cookieSemaphore = new(1, 1);
        private static readonly TimeSpan CookieExpirationTime = TimeSpan.FromMinutes(10);

        public static void ClearCookieCache()
        {
            // Use Wait() for synchronous access to the semaphore
            // This is safe because ClearCookieCache is typically called during logout/cleanup operations
            _cookieSemaphore.Wait();
            try
            {
                System.Diagnostics.Debug.WriteLine("Cookie cache cleared - will fetch fresh cookies from Firefox on next request");
                CachedCookies = null;
                _domainCookieCache.Clear();
                _cookieExpirationTimes.Clear();

                // Also clear persistent cookies when cache is cleared (e.g., on logout)
                CookiePersistenceService.DeleteStoredCookies();
            }
            finally
            {
                _cookieSemaphore.Release();
            }
        }

        public static void ReadChromeVersion()
        {
            try
            {
                if (!ConnectionConfig.CheckoutEnabled)
                    return;

                var path = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData) + @"\Google\Chrome\User Data\Last Version";

                if (!File.Exists(path))
                    return;

                var version = File.ReadAllText(path);
                if (!string.IsNullOrEmpty(version))
                    ProgramState.ChromeUA = @$"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Safari/537.36";
            }
            catch (Exception)
            {
                // ignored
            }
        }

        public static CookieContainer ReadCookiesFirefox(IEnumerable<string> hostNames)
        {
            var hostCookies = new CookieContainer { PerDomainCapacity = 100 };
            if (Profile == null || string.IsNullOrWhiteSpace(Profile.Profile))
            {
                XtraMessageBox.Show("Please, select Firefox profile."); // race condition, but i'll risk it
                return hostCookies;
            }

            var cookiesPath = GetCookiesPathFirefox(Profile.Profile);


            if (!File.Exists(cookiesPath))
            {
                XtraMessageBox.Show("Please, select Firefox profile."); // race condition, but i'll risk it
                return hostCookies;
            }

            var builder = new SQLiteConnectionStringBuilder
            {
                DataSource = cookiesPath,
                FailIfMissing = false,
                ReadOnly = true,
                ForeignKeys = false,
                UseUTF16Encoding = false,
                Pooling = false,
                SyncMode = SynchronizationModes.Off,
                DateTimeKind = DateTimeKind.Utc,
                //DateTimeFormat = SQLiteDateFormats.ISO8601,
                DefaultIsolationLevel = IsolationLevel.ReadCommitted,
                DefaultTimeout = (int)TimeSpan.FromSeconds(15).TotalMilliseconds
            };

            var connectionString = builder.ToString();

            var plainCookies = new List<Tuple<string, string, string>>(); // Store hostKey, encryptedData, and cookie name

            using (var conn = new SQLiteConnection(connectionString))
            {
                conn.Open();
                var inClause = string.Join("', '", hostNames.Select(x => x.Replace("'", "''")));
                using var cmd = conn.CreateCommand();
                cmd.CommandText = $"SELECT name, value, host FROM moz_cookies WHERE host IN ('{inClause}')";

                using var reader = cmd.ExecuteReader();
                while (reader.Read())
                {
                    var hostKey = reader.GetString(2);
                    var encryptedData = reader.GetString(1);
                    var cookieName = reader.GetString(0);

                    plainCookies.Add(Tuple.Create(cookieName, encryptedData, hostKey));
                }

                conn.Close();
            }

            // Decrypt cookies after reading from DB
            foreach (var cookie in plainCookies)
            {
                try
                {
                    var c = new Cookie(cookie.Item1, cookie.Item2, "/", cookie.Item3);
                    // Add special handling for specific domains if needed
                    hostCookies.Add(c);
                }
                catch (Exception ex)
                {
                    // Handle decryption exceptions
                }
            }

            //RebuildCookies(hostCookies);
            CachedCookies = hostCookies;

            return hostCookies;
        }

        /// <summary>
        /// Gets cookies for the specified host names asynchronously.
        /// Returns cached cookies if available, otherwise tries persistent storage, then reads from Firefox.
        /// Cookies are only refreshed when cache is cleared (e.g., on logout detection).
        /// </summary>
        /// <param name="hostNames">Host names to get cookies for</param>
        /// <returns>CookieContainer with cookies for the specified hosts</returns>
        public static async Task<CookieContainer> GetCookiesAsync(IEnumerable<string> hostNames)
        {
            var hostNamesList = hostNames.ToList();
            var cacheKey = string.Join(",", hostNamesList.OrderBy(x => x));
            Debug.WriteLine("Before cookie semaphore");
            await _cookieSemaphore.WaitAsync().ConfigureAwait(false);
            try
            {
                Debug.WriteLine("Cookie semaphore acquired");
                // 1. Check if we have cached cookies in memory
                if (_domainCookieCache.TryGetValue(cacheKey, out var cachedCookies))
                {
                    System.Diagnostics.Debug.WriteLine($"Cookie cache hit for domains: {cacheKey}");
                    return cachedCookies;
                }

                // 2. Try to load from persistent storage
                System.Diagnostics.Debug.WriteLine($"Cookie cache miss - checking persistent storage for domains: {cacheKey}");

                try
                {
                    var persistentCookies = await CookiePersistenceService.LoadCookiesAsync(hostNamesList, Profile).ConfigureAwait(false);
                    if (persistentCookies != null && persistentCookies.TryGetValue(cacheKey, out var persistentContainer) && persistentContainer.Count > 0)
                    {
                        // Cache the persistent cookies in memory
                        _domainCookieCache.AddOrUpdate(cacheKey, persistentContainer, (key, oldValue) => persistentContainer);
                        _cookieExpirationTimes.AddOrUpdate(cacheKey, DateTime.UtcNow.Add(CookieExpirationTime),
                            (key, oldValue) => DateTime.UtcNow.Add(CookieExpirationTime));

                        System.Diagnostics.Debug.WriteLine($"Loaded {persistentContainer.Count} cookies from persistent storage for domains: {cacheKey}");
                        return persistentContainer;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error loading persistent cookies: {ex.Message}");
                    // Continue to Firefox reading
                }

                // 3. Fallback to Firefox reading
                System.Diagnostics.Debug.WriteLine($"No valid persistent cookies - fetching from Firefox for domains: {cacheKey}");

                // Ensure Firefox profile is available before attempting to read cookies
                if (Profile == null || string.IsNullOrWhiteSpace(Profile.Profile))
                {
                    System.Diagnostics.Debug.WriteLine("Firefox profile not set - cannot fetch cookies");
                    // Return empty container but don't cache it, so it will retry next time
                    return new CookieContainer { PerDomainCapacity = 100 };
                }

                var freshCookies = ReadCookiesFirefox(hostNamesList);

                // Only cache cookies if we actually got some (not empty due to profile issues)
                if (freshCookies != null && freshCookies.Count > 0)
                {
                    _domainCookieCache.AddOrUpdate(cacheKey, freshCookies, (key, oldValue) => freshCookies);
                    _cookieExpirationTimes.AddOrUpdate(cacheKey, DateTime.UtcNow.Add(CookieExpirationTime),
                        (key, oldValue) => DateTime.UtcNow.Add(CookieExpirationTime));
                    System.Diagnostics.Debug.WriteLine($"Successfully cached {freshCookies.Count} cookies for domains: {cacheKey}");

                    // 4. Save fresh cookies to persistent storage for next time
                    SaveCookiesToDiskAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"No cookies retrieved from Firefox for domains: {cacheKey}");
                }

                return freshCookies;
            }
            finally
            {
                _cookieSemaphore.Release();
            }
        }

        /// <summary>
        /// Gets cookies for the specified host names synchronously.
        /// This is a wrapper around GetCookiesAsync for backward compatibility.
        /// WARNING: This method can cause deadlocks if called from UI thread. Use GetCookiesAsync instead.
        /// </summary>
        /// <param name="hostNames">Host names to get cookies for</param>
        /// <returns>CookieContainer with cookies for the specified hosts</returns>
        [Obsolete("Use GetCookiesAsync instead to avoid potential deadlocks")]
        public static CookieContainer GetCookies(IEnumerable<string> hostNames)
        {
            return GetCookiesAsync(hostNames).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Ensures cookies are available for the specified domains asynchronously.
        /// If no cookies are cached and Firefox profile is not ready, this will wait briefly and retry.
        /// </summary>
        /// <param name="hostNames">Host names to ensure cookies for</param>
        /// <returns>True if cookies are available, false if unable to get cookies</returns>
        public static async Task<bool> EnsureCookiesAvailableAsync(IEnumerable<string> hostNames)
        {
            const int maxRetries = 3;
            const int retryDelayMs = 1000;

            for (int i = 0; i < maxRetries; i++)
            {
                var cookies = await GetCookiesAsync(hostNames).ConfigureAwait(false);
                if (cookies != null && cookies.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Cookies available for domains: {string.Join(",", hostNames)}");
                    return true;
                }

                if (i < maxRetries - 1)
                {
                    System.Diagnostics.Debug.WriteLine($"No cookies available, retrying in {retryDelayMs}ms (attempt {i + 1}/{maxRetries})");
                    await Task.Delay(retryDelayMs).ConfigureAwait(false);
                }
            }

            System.Diagnostics.Debug.WriteLine($"Failed to get cookies after {maxRetries} attempts for domains: {string.Join(",", hostNames)}");
            return false;
        }

        /// <summary>
        /// Ensures cookies are available for the specified domains synchronously.
        /// This is a wrapper around EnsureCookiesAvailableAsync for backward compatibility.
        /// WARNING: This method can cause deadlocks if called from UI thread. Use EnsureCookiesAvailableAsync instead.
        /// </summary>
        /// <param name="hostNames">Host names to ensure cookies for</param>
        /// <returns>True if cookies are available, false if unable to get cookies</returns>
        [Obsolete("Use EnsureCookiesAvailableAsync instead to avoid potential deadlocks")]
        public static bool EnsureCookiesAvailable(IEnumerable<string> hostNames)
        {
            return EnsureCookiesAvailableAsync(hostNames).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Updates cached cookies from HTTP response headers asynchronously.
        /// Extracts Set-Cookie headers and replaces existing cookies in the cached cookie container.
        /// </summary>
        /// <param name="response">HTTP response message containing Set-Cookie headers</param>
        /// <param name="hostNames">Host names that the cookies apply to</param>
        public static async Task UpdateCookiesFromResponseAsync(HttpResponseMessage response, IEnumerable<string> hostNames)
        {
            try
            {
                var hostNamesList = hostNames.ToList();
                var cacheKey = string.Join(",", hostNamesList.OrderBy(x => x));

                await _cookieSemaphore.WaitAsync().ConfigureAwait(false);
                try
                {
                    // Create a new cookie container to replace existing cookies
                    var cookieContainer = new CookieContainer { PerDomainCapacity = 100 };

                    // Extract cookies from Set-Cookie headers
                    if (response.Headers.TryGetValues("Set-Cookie", out var setCookieHeaders))
                    {
                        foreach (var setCookieHeader in setCookieHeaders)
                        {
                            try
                            {
                                // Parse the Set-Cookie header and add to new container
                                var uri = response.RequestMessage?.RequestUri;
                                if (uri != null)
                                {
                                    cookieContainer.SetCookies(uri, setCookieHeader);
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log cookie parsing errors but continue processing
                                Debug.WriteLine($"Error parsing cookie: {setCookieHeader}, Error: {ex.Message}");
                            }
                        }
                    }

                    // Replace the cached container entirely with the new one
                    _domainCookieCache.AddOrUpdate(cacheKey, cookieContainer, (key, oldValue) => cookieContainer);

                    // Update expiration time since we have fresh cookies
                    _cookieExpirationTimes.AddOrUpdate(cacheKey, DateTime.UtcNow.Add(CookieExpirationTime),
                        (key, oldValue) => DateTime.UtcNow.Add(CookieExpirationTime));

                    // Save updated cookies to persistent storage
                    SaveCookiesToDiskAsync();
                }
                finally
                {
                    _cookieSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                // Log but don't throw - cookie updates shouldn't break the main flow
                Debug.WriteLine($"Error updating cookies from response: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates cached cookies from HTTP response headers synchronously.
        /// This is a wrapper around UpdateCookiesFromResponseAsync for backward compatibility.
        /// WARNING: This method can cause deadlocks if called from UI thread. Use UpdateCookiesFromResponseAsync instead.
        /// </summary>
        /// <param name="response">HTTP response message containing Set-Cookie headers</param>
        /// <param name="hostNames">Host names that the cookies apply to</param>
        [Obsolete("Use UpdateCookiesFromResponseAsync instead to avoid potential deadlocks")]
        public static void UpdateCookiesFromResponse(HttpResponseMessage response, IEnumerable<string> hostNames)
        {
            UpdateCookiesFromResponseAsync(response, hostNames).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Initializes cookies by pre-loading cookies for common eBay domains asynchronously.
        /// This method is no longer called automatically at startup - cookies are now fetched on-demand.
        /// Can be called manually if pre-loading is desired for performance reasons.
        /// </summary>
        public static async Task InitializeCookiesAsync()
        {
            try
            {
                if (!ConnectionConfig.CheckoutEnabled || Profile == null)
                    return;

                // Pre-load cookies for common eBay domains
                var commonDomains = new[] { ".ebay.com", ".ebay.co.uk", ".ebay.de", ".ebay.fr", ".ebay.it", ".ebay.es" };

                // This will cache cookies for future use (and load from persistent storage if available)
                await GetCookiesAsync(commonDomains).ConfigureAwait(false);

                Debug.WriteLine("Cookie initialization completed for common eBay domains");
            }
            catch (Exception ex)
            {
                // Log but don't throw - initialization failures shouldn't break startup
                Debug.WriteLine($"Error during cookie initialization: {ex.Message}");
            }
        }

        /// <summary>
        /// Initializes cookies by pre-loading cookies for common eBay domains synchronously.
        /// This is a wrapper around InitializeCookiesAsync for backward compatibility.
        /// WARNING: This method can cause deadlocks if called from UI thread. Use InitializeCookiesAsync instead.
        /// </summary>
        [Obsolete("Use InitializeCookiesAsync instead to avoid potential deadlocks")]
        public static void InitializeCookies()
        {
            InitializeCookiesAsync().GetAwaiter().GetResult();
        }

        /// <summary>
        /// Initializes persistent cookie storage by attempting to load stored cookies for the current profile
        /// </summary>
        public static async Task InitializePersistentCookiesAsync()
        {
            try
            {
                if (Profile == null || string.IsNullOrEmpty(Profile.Profile))
                {
                    Debug.WriteLine("Cannot initialize persistent cookies - no profile set");
                    return;
                }

                // Try to load any existing persistent cookies into memory cache
                var commonDomains = new[] { ".ebay.com", ".ebay.co.uk", ".ebay.de", ".ebay.fr", ".ebay.it", ".ebay.es" };
                var cacheKey = string.Join(",", commonDomains.OrderBy(x => x));

                var persistentCookies = await CookiePersistenceService.LoadCookiesAsync(commonDomains, Profile).ConfigureAwait(false);
                if (persistentCookies != null && persistentCookies.TryGetValue(cacheKey, out var cookieContainer) && cookieContainer.Count > 0)
                {
                    await _cookieSemaphore.WaitAsync().ConfigureAwait(false);
                    try
                    {
                        _domainCookieCache.AddOrUpdate(cacheKey, cookieContainer, (key, oldValue) => cookieContainer);
                        _cookieExpirationTimes.AddOrUpdate(cacheKey, DateTime.UtcNow.Add(CookieExpirationTime),
                            (key, oldValue) => DateTime.UtcNow.Add(CookieExpirationTime));
                    }
                    finally
                    {
                        _cookieSemaphore.Release();
                    }

                    Debug.WriteLine($"Initialized {cookieContainer.Count} persistent cookies for common eBay domains");
                }
                else
                {
                    Debug.WriteLine("No valid persistent cookies found for initialization");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing persistent cookies: {ex.Message}");
                // Don't throw - initialization failures shouldn't break startup
            }
        }

        internal static string GetCookiesPathFirefox(string? profilePath)
        {
            var localDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

            // Ensure profilePath doesn't already contain the "Profiles" directory
            var cookiesPath = Path.Combine(localDataPath, "Mozilla", "Firefox", profilePath, "cookies.sqlite");

            // Handle the case where the profilePath might already include a "Profiles" folder
            if (cookiesPath.Contains("Profiles" + Path.DirectorySeparatorChar + "Profiles" + Path.DirectorySeparatorChar))
            {
                cookiesPath = cookiesPath.Replace('/', Path.DirectorySeparatorChar);
            }

            return cookiesPath;
        }

        internal static List<CookieProfile> GetProfileNamesFirefox()
        {
            var profiles = new List<CookieProfile>();
            var localDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var firefoxProfilePath = $@"{localDataPath}\Mozilla\Firefox\profiles.ini";



            if (!File.Exists(firefoxProfilePath))
                return profiles;

            var profileContent = File.ReadAllLines(firefoxProfilePath);
            string? profileName = null;
            string? profilePath = null;

            foreach (var line in profileContent)
            {
                if (line.StartsWith("Name="))
                {
                    profileName = line.Substring("Name=".Length);
                }
                else if (line.StartsWith("Path="))
                {
                    profilePath = line.Substring("Path=".Length);
                }

                if (!string.IsNullOrEmpty(profileName) && !string.IsNullOrEmpty(profilePath))
                {
                    var cookiePath = GetCookiesPathFirefox(profilePath);
                    var signedInUserPath = Path.Combine(Path.GetDirectoryName(cookiePath) ?? string.Empty, "signedInUser.json"); // Path to signedInUser.json

                    string? userEmail = null;

                    // Check if signedInUser.json exists and get the email
                    if (File.Exists(signedInUserPath))
                    {
                        var signedInUserJson = File.ReadAllText(signedInUserPath);
                        var signedInUser = JsonConvert.DeserializeObject<SignedInUser>(signedInUserJson);
                        userEmail = signedInUser?.accountData?.email; // Extract email
                    }
                    profiles.Add(new CookieProfile()
                    {
                        Name = (userEmail != null ? $"{userEmail}" : profileName), // Append email if available
                        Profile = profilePath
                    });
                    profileName = null;
                    profilePath = null; // Reset after adding the profile
                }
            }

            return profiles;
        }

        public class SignedInUser
        {
            public AccountData accountData { get; set; }
        }

        public class AccountData
        {
            public string email { get; set; }
        }

        /// <summary>
        /// Asynchronously saves current cookie cache to persistent storage
        /// </summary>
        private static async void SaveCookiesToDiskAsync()
        {
            try
            {
                // Don't save if no profile is set
                if (Profile == null || string.IsNullOrEmpty(Profile.Profile))
                    return;

                // Save current cache to disk (fire and forget)
                await CookiePersistenceService.SaveCookiesAsync(_domainCookieCache, Profile);
            }
            catch (Exception ex)
            {
                // Log but don't throw - persistence failure shouldn't break main flow
                System.Diagnostics.Debug.WriteLine($"Error saving cookies to disk: {ex.Message}");
            }
        }
    }

    public class CookieProfile
    {
        public string? Name { get; set; }
        public string? Profile { get; set; }

        public override string? ToString()
        {
            return Name;
        }
    }

    public class LocalState
    {
        [JsonProperty("profile")]
        public Profile? Profile { get; set; }
    }

    public class Profile
    {
        [JsonProperty("info_cache")]
        public Dictionary<string, ProfileInfo>? InfoCache { get; set; }
    }

    public class ProfileInfo
    {
        [JsonProperty("name")]
        public string? Name { get; set; }
    }
}
