using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Data;
using uBuyFirst.Intl;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Purchasing;
using uBuyFirst.Search.Status;

namespace uBuyFirst.Tests.Purchasing
{
    [TestClass]
    public class CheckoutFailedStatusTests
    {
        private RestockModeEnum _originalRestockMode;

        [TestInitialize]
        public void TestInitialize()
        {
            // Save the original restock mode to restore after tests
            _originalRestockMode = UserSettings.RestockMode;
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Restore the original restock mode
            UserSettings.RestockMode = _originalRestockMode;
        }

        /// <summary>
        /// Creates a test EBaySite for testing purposes
        /// </summary>
        private static EBaySite CreateTestEBaySite()
        {
            // Use the US eBay site format from CountryProvider
            return new EBaySite("EBAY-US\tEBAY_US\ten-US\tUS\teBay US\t0\tUS\tebay.com\tpicsuffix\t1\thttps://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466");
        }

        [TestMethod]
        public void LiveMode_PaymentFailed_ShouldSetCheckoutFailedStatus()
        {
            // Arrange
            UserSettings.RestockMode = RestockModeEnum.LiveMode;

            var dataList = new DataList();
            dataList.Order = new BuyingService.BuyOrder("123456", "Test Item", CreateTestEBaySite(), 1, new CurrencyAmount(9.99, "USD"), Placeoffer.OrderAction.PayWithCreditCard);
            dataList.Order.CheckoutStatus = BuyingService.Order.CheckoutState.PaymentFailed;
            dataList.Order.FailureReasonMessage = "Payment failed due to insufficient funds";

            // Act
            // Simulate the UpdateStatusAfterCheckout logic
            var failureStatus = UserSettings.RestockMode == RestockModeEnum.LiveMode
                ? ItemStatus.CheckoutFailed
                : ItemStatus.Unknown;
            dataList.SetStatus(failureStatus);

            // Assert
            Assert.AreEqual(ItemStatus.CheckoutFailed, dataList.ItemStatus);
        }

        [TestMethod]
        public void TestMode_PaymentFailed_ShouldSetUnknownStatus()
        {
            // Arrange
            UserSettings.RestockMode = RestockModeEnum.TestMode;

            var dataList = new DataList();
            dataList.Order = new BuyingService.BuyOrder("123456", "Test Item", CreateTestEBaySite(), 1, new CurrencyAmount(9.99, "USD"), Placeoffer.OrderAction.PayWithCreditCard);
            dataList.Order.CheckoutStatus = BuyingService.Order.CheckoutState.PaymentFailed;
            dataList.Order.FailureReasonMessage = "Payment failed due to insufficient funds";

            // Act
            // Simulate the UpdateStatusAfterCheckout logic
            var failureStatus = UserSettings.RestockMode == RestockModeEnum.LiveMode
                ? ItemStatus.CheckoutFailed
                : ItemStatus.Unknown;
            dataList.SetStatus(failureStatus);

            // Assert
            Assert.AreEqual(ItemStatus.Unknown, dataList.ItemStatus);
        }

        [TestMethod]
        public void LiveMode_SessionCreationFailed_ShouldSetCheckoutFailedStatus()
        {
            // Arrange
            UserSettings.RestockMode = RestockModeEnum.LiveMode;

            var dataList = new DataList();
            dataList.Order = new BuyingService.BuyOrder("123456", "Test Item", CreateTestEBaySite(), 1, new CurrencyAmount(9.99, "USD"), Placeoffer.OrderAction.PayWithCreditCard);
            dataList.Order.CheckoutStatus = BuyingService.Order.CheckoutState.SessionCreationFailed;
            dataList.Order.FailureReasonMessage = "Failed to create session";

            // Act
            // Simulate the UpdateStatusAfterCheckout logic
            var failureStatus = UserSettings.RestockMode == RestockModeEnum.LiveMode
                ? ItemStatus.CheckoutFailed
                : ItemStatus.Unknown;
            dataList.SetStatus(failureStatus);

            // Assert
            Assert.AreEqual(ItemStatus.CheckoutFailed, dataList.ItemStatus);
        }

        [TestMethod]
        public void CheckoutFailedStatus_ShouldNotBeResetBySetUnknownToActive()
        {
            // Arrange
            var dataList = new DataList();
            dataList.SetStatus(ItemStatus.CheckoutFailed);

            // Simulate conditions that would normally trigger SetUnknownToActive
            // This test verifies that CheckoutFailed status is excluded from automatic transitions

            // Act
            // The SetUnknownToActive method should not change CheckoutFailed status
            // because it only processes Active, Unknown, and Updated statuses
            var originalStatus = dataList.ItemStatus;

            // Assert
            Assert.AreEqual(ItemStatus.CheckoutFailed, originalStatus);
            // CheckoutFailed should remain unchanged by status transition logic
        }

        [TestMethod]
        public void CheckoutFailedStatus_ShouldNotBeProcessedByStatusUpdates()
        {
            // Arrange
            var dataList = new DataList();
            dataList.SetStatus(ItemStatus.CheckoutFailed);

            // Act & Assert
            // Verify that CheckoutFailed status is not in the list of statuses
            // that get processed by automatic status update methods
            var isProcessableStatus = dataList.ItemStatus == ItemStatus.Active ||
                                    dataList.ItemStatus == ItemStatus.Unknown ||
                                    dataList.ItemStatus == ItemStatus.Updated;

            Assert.IsFalse(isProcessableStatus, "CheckoutFailed status should not be processed by automatic status updates");
        }

        [TestMethod]
        public void OtherFailureStatuses_ShouldNotBeAffectedByCheckoutFailedChanges()
        {
            // Arrange & Act & Assert
            // Verify that other failure statuses continue to work as expected
            // and are properly excluded from processing

            var dataList1 = new DataList();
            dataList1.SetStatus(ItemStatus.Incorrect);
            Assert.AreEqual(ItemStatus.Incorrect, dataList1.ItemStatus);

            var dataList2 = new DataList();
            dataList2.SetStatus(ItemStatus.NotAvailable);
            Assert.AreEqual(ItemStatus.NotAvailable, dataList2.ItemStatus);

            var dataList3 = new DataList();
            dataList3.SetStatus(ItemStatus.OtherListingError);
            Assert.AreEqual(ItemStatus.OtherListingError, dataList3.ItemStatus);

            var dataList4 = new DataList();
            dataList4.SetStatus(ItemStatus.LostOrBroken);
            Assert.AreEqual(ItemStatus.LostOrBroken, dataList4.ItemStatus);

            var dataList5 = new DataList();
            dataList5.SetStatus(ItemStatus.SellToHighBidder);
            Assert.AreEqual(ItemStatus.SellToHighBidder, dataList5.ItemStatus);

            // Verify these statuses are excluded from processing (same logic as CheckoutFailed)
            var statuses = new[] { ItemStatus.Incorrect, ItemStatus.NotAvailable, ItemStatus.OtherListingError,
                                 ItemStatus.LostOrBroken, ItemStatus.SellToHighBidder, ItemStatus.CheckoutFailed };

            foreach (var status in statuses)
            {
                var isProcessableStatus = status == ItemStatus.Active ||
                                        status == ItemStatus.Unknown ||
                                        status == ItemStatus.Updated;
                Assert.IsFalse(isProcessableStatus, $"{status} should not be processed by automatic status updates");
            }
        }

        [TestMethod]
        public void DisabledMode_CheckoutFailures_ShouldUseExistingBehavior()
        {
            // Arrange
            UserSettings.RestockMode = RestockModeEnum.Disabled;

            var dataList = new DataList();
            dataList.Order = new BuyingService.BuyOrder("123456", "Test Item", CreateTestEBaySite(), 1, new CurrencyAmount(9.99, "USD"), Placeoffer.OrderAction.PayWithCreditCard);
            dataList.Order.CheckoutStatus = BuyingService.Order.CheckoutState.PaymentFailed;

            // Act
            // Simulate the UpdateStatusAfterCheckout logic
            var failureStatus = UserSettings.RestockMode == RestockModeEnum.LiveMode
                ? ItemStatus.CheckoutFailed
                : ItemStatus.Unknown;
            dataList.SetStatus(failureStatus);

            // Assert
            // Disabled mode should use Unknown status (existing behavior)
            Assert.AreEqual(ItemStatus.Unknown, dataList.ItemStatus);
        }
    }
}
