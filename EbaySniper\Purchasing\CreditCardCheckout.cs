﻿using System;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Prefs;
using uBuyFirst.Purchasing.Cookies;
using uBuyFirst.Restocker.Services;
using uBuyFirst.Tools;

namespace uBuyFirst.Purchasing
{
    public static class CreditCardCheckout
    {
        public static async Task ExecuteCreditCardCheckout(DataList d, int quantityToPurchase, bool isRestockPurchase = false)
        {
            var quantity = quantityToPurchase;

            if (d.Order is not { OrderAction: Placeoffer.OrderAction.PayWithCreditCard })
            {
                var effectivePurchasePrice = d.ItemPricing.GetEffectivePurchasePrice();
                d.Order = new BuyingService.BuyOrder(d.ItemID, d.Title, d.EBaySite, quantity, effectivePurchasePrice, Placeoffer.OrderAction.PayWithCreditCard);
            }

            // Set restock flag for HTML logging
            if (isRestockPurchase && d.Order != null)
            {
                d.Order.IsRestockPurchase = true;
            }

            PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies started");

            // Ensure cookies are available before proceeding with checkout
            var domains = new[] { $".{d.Order.EbaySite.Domain}" };
            if (!await CookieManager.EnsureCookiesAvailableAsync(domains).ConfigureAwait(false))
            {
                d.Order.CheckoutStatus = BuyingService.Order.CheckoutState.SessionCreationFailed;
                d.Order.FailureReasonMessage = "Failed to load cookies from Firefox profile - please ensure Firefox profile is selected and contains eBay cookies";

                // Use CheckoutFailed for Live mode, Incorrect for other modes
                var failureStatus = UserSettings.RestockMode == RestockModeEnum.LiveMode
                    ? ItemStatus.CheckoutFailed
                    : ItemStatus.Incorrect;
                SafeSetStatus(d, failureStatus);

                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies failed - no cookies available");
                return;
            }

            d.Order.CookieContainer = await CookieManager.GetCookiesAsync(domains).ConfigureAwait(false);
            PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies stopped");
            switch (d.Order.CheckoutStatus)
            {
                case BuyingService.Order.CheckoutState.NotStarted:
                    // UI thread status updates - before and after async operations
                    SafeSetStatus(d, ItemStatus.CreatingSession);

                    await CreditCardService.CreditCartCheckoutCompleteSequence(d);

                    // Update status based on operation result (still on UI thread)
                    UpdateStatusAfterCheckout(d);
                    //await ShowUserMessageAfterPurchaseAttempt(d);
                    break;

                case BuyingService.Order.CheckoutState.CreatingSession:
                    d.Order.AutoConfirmationAllowed = true;
                    break;

                case BuyingService.Order.CheckoutState.SessionCreated:
                    // UI thread status update
                    SafeSetStatus(d, ItemStatus.PaymentInProgress);
                    d.Order.AutoConfirmationAllowed = true;

                    await CreditCardService.ConfirmCreditCardPayment((BuyingService.BuyOrder)d.Order, d.Title);

                    // Update status based on operation result (still on UI thread)
                    UpdateStatusAfterCheckout(d);
                    await ShowUserMessageAfterPurchaseAttempt(d);
                    break;
            }
        }

        /// <summary>
        /// Thread-safe method to set DataList status using UI synchronization context
        /// </summary>
        private static void SafeSetStatus(DataList dataList, ItemStatus status)
        {
            if (Form1.Instance?.InvokeRequired == true)
            {
                Form1.Instance.Invoke(() => dataList.SetStatus(status));
            }
            else
            {
                dataList.SetStatus(status);
            }
        }

        /// <summary>
        /// Updates the DataList status based on the checkout operation result
        /// This method ensures UI updates happen on the main thread
        /// </summary>
        private static void UpdateStatusAfterCheckout(DataList d)
        {
            // Determine the appropriate failure status based on the current restock mode
            // In Live mode, use CheckoutFailed to persist failure visibility
            // In Test mode, continue using Unknown for existing behavior
            var failureStatus = UserSettings.RestockMode == RestockModeEnum.LiveMode
                ? ItemStatus.CheckoutFailed
                : ItemStatus.Unknown;

            var finalStatus = d.Order.CheckoutStatus switch
            {
                BuyingService.Order.CheckoutState.PaymentSuccess => ItemStatus.Sold,
                BuyingService.Order.CheckoutState.TestPurchase => ItemStatus.TestPurchase,
                BuyingService.Order.CheckoutState.SessionCreationFailed => failureStatus,
                BuyingService.Order.CheckoutState.PaymentFailed => failureStatus,
                _ => failureStatus
            };

            SafeSetStatus(d, finalStatus);
        }

        /// <summary>
        /// Shows user notification after purchase attempt
        /// Status updates are handled separately by UpdateStatusAfterCheckout
        /// </summary>
        private static async Task ShowUserMessageAfterPurchaseAttempt(DataList d)
        {
            // Check the final status on the order object after the confirmation completes
            if (d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess
                || d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
            {
                // Instantiate FlyoutPanelSnackBar directly
                string successMessage;
                string alertTitle = "Purchase Successful";

                if (d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
                {
                    successMessage = $"Test purchase successful for {d.Title}.";
                    alertTitle = "Test Mode Purchase Successful";
                    // Status update removed - handled by UpdateStatusAfterCheckout
                }
                else
                {
                    // Add Live Mode context to success messages
                    if (UserSettings.RestockMode == RestockModeEnum.LiveMode)
                    {
                        successMessage = $"Live Mode purchase successful for {d.Title}.";
                        alertTitle = "Live Mode Purchase Successful";
                    }
                    else
                    {
                        successMessage = $"Credit card payment successful for {d.Title}.";
                    }
                    // Status update removed - handled by UpdateStatusAfterCheckout
                }

                if (d.GridControl != null)
                {
                    // Ensure UI operations happen on the UI thread
                    d.GridControl.InvokeIfRequired(() =>
                    {
                        try
                        {
                            // Check if control is still valid before creating FlyoutPanelSnackBar
                            if (d.GridControl != null && !d.GridControl.IsDisposed && d.GridControl.IsHandleCreated)
                            {
                                var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                                flyoutSnackBar.ShowSuccess(d.GridControl, successMessage);
                                // FlyoutPanelSnackBar handles its own disposal
                            }
                            else
                            {
                                // Fallback to alert notification if GridControl is invalid
                                ShowAlertNotification(alertTitle, successMessage);
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log the exception and fallback to alert notification
                            System.Diagnostics.Debug.WriteLine($"Error showing FlyoutPanelSnackBar: {ex.Message}");
                            ShowAlertNotification(alertTitle, successMessage);
                        }
                    });
                }
                else
                {
                    // Show alert notification when GridControl is not available (e.g., in restock context)
                    ShowAlertNotification(alertTitle, successMessage);
                }
            }
            else // Handle various failure states (PaymentFailed, ConfirmationFailed, etc.)
            {
                var failureMessage = d.Order.FailureReasonMessage ?? "Credit card payment failed (Unknown reason).";

                // Check if this is a captcha-related error
                if (CaptchaCooldownManager.IsCaptchaError(failureMessage))
                {
                    await CaptchaCooldownManager.HandleCaptchaDetectionAsync(d, failureMessage, startCooldown: false);
                    return;
                }

                // Add Live Mode context to failure notifications
                var modeContext = UserSettings.RestockMode == RestockModeEnum.LiveMode ? " (Live Mode)" :
                                 UserSettings.RestockMode == RestockModeEnum.TestMode ? " (Test Mode)" : "";
                var fullFailureMessage = $"Credit card payment failed{modeContext} for {d.Title}: {failureMessage}";
                var alertTitle = UserSettings.RestockMode == RestockModeEnum.LiveMode ? "Live Mode Purchase Failed" : "Purchase Failed";

                ShowAlertNotification(alertTitle, fullFailureMessage);
            }
        }



        private static void ShowAlertNotification(string alertCaption, string alertText)
        {
            try
            {
                // Check if running on Windows 7
                var isWindows7 = Environment.OSVersion.Version.Major == 6 && Environment.OSVersion.Version.Minor < 2 || Environment.OSVersion.Version.Major < 6;
                if (isWindows7)
                {
                    var alert = new DevExpress.XtraBars.Alerter.AlertControl();
                    alert.AllowHtmlText = true;
                    alert.AutoHeight = true;
                    alert.FormShowingEffect = DevExpress.XtraBars.Alerter.AlertFormShowingEffect.SlideHorizontal;
                    alert.ShowPinButton = true;

                    // Get the main form as the owner
                    var mainForm = Form1.Instance;
                    if (mainForm != null)
                    {
                        alert.Show(mainForm, alertCaption, alertText);
                    }
                }
                else
                {
                    // For non-Windows 7, just log the message
                    PaymentLogger.LogPaymentToFile($"[Alert] {alertCaption}: {alertText}");
                }
            }
            catch (Exception ex)
            {
                // Fallback to logging if alert fails
                PaymentLogger.LogPaymentToFile($"[Alert Failed] {alertCaption}: {alertText}. Error: {ex.Message}");
            }
        }
    }
}
