﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web.Services.Protocols;
using uBuyFirst.Prefs;
using uBuyFirst.Purchasing.Cookies;
using uBuyFirst.Tools;

namespace uBuyFirst.Network
{
    public static class NetTools
    {
        /// <summary>
        /// Stores the most recently extracted srt parameter for captcha submission
        /// </summary>
        public static string? LastExtractedSrtParameter { get; private set; }
        public static string? LastCaptchaUrl { get; private set; }

        /// <summary>
        /// Downloads the captcha page and extracts the srt parameter from the HTML form
        /// </summary>
        /// <param name="captchaUrl">The captcha page URL from Location header</param>
        /// <param name="cookies">Cookie container for the request</param>
        /// <returns>The srt parameter value if found, null otherwise</returns>
        internal static async Task<string?> ExtractSrtFromCaptchaPageAsync(string captchaUrl, CookieContainer cookies)
        {
            if (string.IsNullOrEmpty(captchaUrl))
                return null;

            try
            {
                Debug.WriteLine($"Downloading captcha page to extract srt: {captchaUrl}");

                // Download the captcha page HTML (skip srt extraction to avoid recursion)
                var captchaPageHtml = await FetchUrlUsingCookiesAsync2(captchaUrl, cookies, skipSrtExtraction: true);

                if (string.IsNullOrEmpty(captchaPageHtml))
                {
                    Debug.WriteLine("Failed to download captcha page HTML");
                    return null;
                }

                // Extract srt value from hidden form field: <input type="hidden" name="srt" value="...">
                var srtMatch = System.Text.RegularExpressions.Regex.Match(
                    captchaPageHtml,
                    @"<input[^>]*name=[""']srt[""'][^>]*value=[""']([^""']+)[""'][^>]*>",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                if (srtMatch.Success)
                {
                    var srtValue = srtMatch.Groups[1].Value;
                    Debug.WriteLine($"Extracted srt from captcha page HTML: {srtValue.Substring(0, Math.Min(50, srtValue.Length))}...");
                    return srtValue;
                }

                Debug.WriteLine("Could not find srt parameter in captcha page HTML");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error extracting srt from captcha page: {ex.Message}");
                return null;
            }
        }

        public static async Task<string> FetchUrlUsingCookiesAsync(string url, CookieContainer cookies, bool skipSrtExtraction = false)
        {
            for (var i = 0; i < 4; i++)
            {
                try
                {
                    ServicePointManager.Expect100Continue = false;
                    ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;

                    var baseAddress = new Uri(url);
                    var handler = new Http2CustomHandler(Helpers.BuyItNowConfirmation)
                    {
                        CookieContainer = cookies,
                        CookieUsePolicy = CookieUsePolicy.UseSpecifiedCookieContainer,
                        //var result = await httpClient.GetStringAsync(baseAddress);
                        AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                        WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseWinInetProxy
                    };
                    if (Debugger.IsAttached || Program.AffiliateOff)
                    {
                        //handler.Proxy = new WebProxy(new Uri("http://127.0.0.1:8889"));
                        //handler.WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseCustomProxy;
                    }

                    var chromeVersionShort = Helpers.RegexValue(ProgramState.ChromeUA, "Chrome/([0-9]+)");
                    var chromeVersionLong = Helpers.RegexValue(ProgramState.ChromeUA, "Chrome/([0-9\\.]+)");
                    var sec_ch_ua = $"\".Not/A)Brand\";v=\"24\", \"Google Chrome\";v=\"{chromeVersionShort}\", \"Chromium\";v=\"{chromeVersionShort}\"";

                    using (handler)
                    //using (var httpClient = new HttpClient(handler)
                    using (var httpClient = new HttpClient(handler))
                    {
                        httpClient.BaseAddress = baseAddress;
                        httpClient.Timeout = new TimeSpan(0, 0, 10);
                        httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-us,en;q=0.5");
                        httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip,deflate");
                        httpClient.DefaultRequestHeaders.Add("Accept-Charset", "utf-8;q=0.7,*;q=0.7");
                        httpClient.DefaultRequestHeaders.Add("User-Agent", ProgramState.ChromeUA.Replace(chromeVersionLong, chromeVersionShort + ".0.0.0"));

                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua", sec_ch_ua);
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-mobile", "?0");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform", "\"Windows\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform-version", "\"10.0.0\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-model", "\"\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-full-version", $"\"{chromeVersionLong}\"");

                        httpClient.DefaultRequestHeaders.Add("upgrade-insecure-requests", "1");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-site", "none");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-mode", "navigate");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-user", "?1");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-dest", "document");

                        httpClient.DefaultRequestHeaders.Add("Accept",
                            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.98");

                        var request = new HttpRequestMessage(HttpMethod.Get, new Uri(url)) { Version = new Version(2, 0), };
                        var httpResponseMessage = await httpClient.SendAsync(request);

                        if (httpResponseMessage?.RequestMessage?.RequestUri?.AbsoluteUri?.Contains(Config.CaptchaUrl) == true)
                        {
                            LastCaptchaUrl = httpResponseMessage?.RequestMessage?.RequestUri?.AbsoluteUri;
                            return Config.CaptchaUrl;
                        }

                        if (httpResponseMessage?.RequestMessage?.RequestUri?.AbsoluteUri?.Contains(Config.EbaySignInUrl) == true)
                        {
                            return Config.EbaySignInUrl;
                        }

                        if (httpResponseMessage?.Content != null)
                        {
                            var result = await httpResponseMessage.Content.ReadAsStringAsync();

                            // Update cookies from response to maintain session freshness
                            try
                            {
                                var hostName = baseAddress.Host;
                                if (hostName.StartsWith("www."))
                                    hostName = hostName.Substring(4);
                                await CookieManager.UpdateCookiesFromResponseAsync(httpResponseMessage, new[] { $".{hostName}" }).ConfigureAwait(false);
                            }
                            catch (Exception ex)
                            {
                                // Log but don't throw - cookie updates shouldn't break the main flow
                                Debug.WriteLine($"Error updating cookies from response: {ex.Message}");
                            }

                            //var result = await httpClient.GetStringAsync(baseAddress);

                            return result;
                        }
                    }
                }
                catch (Exception ex)
                {
                    ExM.ubuyExceptionHandler("Checkout: ", ex);
                }
            }

            return "";
        }
        public static async Task<string> FetchUrlUsingCookiesAsync2(string url, CookieContainer cookies, bool skipSrtExtraction = false)
        {
            for (var i = 0; i < 4; i++)
            {
                try
                {
                    ServicePointManager.Expect100Continue = false;
                    ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;

                    var baseAddress = new Uri(url);
                    var handler = new Http2CustomHandler(Helpers.BuyItNowConfirmation)
                    {
                        CookieContainer = cookies,
                        CookieUsePolicy = CookieUsePolicy.UseSpecifiedCookieContainer,
                        //var result = await httpClient.GetStringAsync(baseAddress);
                        AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                        WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseWinInetProxy
                    };
                    if (Debugger.IsAttached || Program.AffiliateOff)
                    {
                        //handler.Proxy = new WebProxy(new Uri("http://127.0.0.1:8889"));
                        //handler.WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseCustomProxy;
                    }

                    var chromeVersionShort = Helpers.RegexValue(ProgramState.ChromeUA, "Chrome/([0-9]+)");
                    var chromeVersionLong = Helpers.RegexValue(ProgramState.ChromeUA, "Chrome/([0-9\\.]+)");
                    var sec_ch_ua = $"\".Not/A)Brand\";v=\"24\", \"Google Chrome\";v=\"{chromeVersionShort}\", \"Chromium\";v=\"{chromeVersionShort}\"";

                    using (handler)
                    //using (var httpClient = new HttpClient(handler)
                    using (var httpClient = new HttpClient(handler))
                    {
                        httpClient.BaseAddress = baseAddress;
                        httpClient.Timeout = new TimeSpan(0, 0, 10);
                        httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-us,en;q=0.5");
                        httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip,deflate");
                        httpClient.DefaultRequestHeaders.Add("Accept-Charset", "utf-8;q=0.7,*;q=0.7");
                        httpClient.DefaultRequestHeaders.Add("User-Agent", ProgramState.ChromeUA.Replace(chromeVersionLong, chromeVersionShort + ".0.0.0"));

                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua", sec_ch_ua);
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-mobile", "?0");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform", "\"Windows\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform-version", "\"10.0.0\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-model", "\"\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-full-version", $"\"{chromeVersionLong}\"");

                        httpClient.DefaultRequestHeaders.Add("upgrade-insecure-requests", "1");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-site", "none");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-mode", "navigate");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-user", "?1");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-dest", "document");

                        httpClient.DefaultRequestHeaders.Add("Accept",
                            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.98");

                        var request = new HttpRequestMessage(HttpMethod.Get, new Uri(url)) { Version = new Version(2, 0), };
                        var httpResponseMessage = await httpClient.SendAsync(request);

                        if (httpResponseMessage?.Content != null)
                        {
                            var result = await httpResponseMessage.Content.ReadAsStringAsync();
                            return result;
                        }
                    }
                }
                catch (Exception ex)
                {
                    ExM.ubuyExceptionHandler("Checkout: ", ex);
                }
            }

            return "";
        }
        public static void InitHttPprotocol()
        {
            var type = typeof(HttpWebClientProtocol);
            var ua = type.GetField("UserAgentDefault", BindingFlags.Static | BindingFlags.NonPublic);
            ua?.SetValue(null, ProgramState.ChromeUA);
            ServicePointManager.Expect100Continue = false;
            ServicePointManager.DefaultConnectionLimit = 100;
        }

        public static string ShoppingApiCall(string itemIDs, string callName, string detailLevel)
        {
            //"&IncludeSelector=Details"
            try
            {
                var url = $"{ConnectionConfig.ShoppingApiHost}"
                          + $"/shopping?{detailLevel}callname={callName}&appid={ConnectionConfig.ShoppingApiKey}&responseencoding=XML&siteid=0&version=1081&ItemID={itemIDs}";
                var httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
                httpWebRequest.Timeout = 5 * 1000;
                httpWebRequest.ReadWriteTimeout = 5 * 1000;
                httpWebRequest.ContentType = "application/x-www-form-urlencoded";
                httpWebRequest.Method = "GET";
                httpWebRequest.UserAgent = ProgramState.ChromeUA;
                httpWebRequest.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
                httpWebRequest.Referer = url;
                httpWebRequest.Headers.Add("Accept-Language", "en-us,en;q=0.5");
                httpWebRequest.Headers.Add("Accept-Encoding", "gzip,deflate");
                httpWebRequest.Headers.Add("Accept-Charset", "utf-8;q=0.7,*;q=0.7");
                httpWebRequest.AllowAutoRedirect = false;
                httpWebRequest.AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate;
                var response = (HttpWebResponse)httpWebRequest.GetResponse();
                var receiveStream = response.GetResponseStream();
                if (receiveStream != null)
                {
                    var readStream = new StreamReader(receiveStream, Encoding.UTF8);
                    var result = readStream.ReadToEnd();
                    response.Close();
                    readStream.Close();

                    if (result.Contains("<Ack>Failure</Ack>"))
                        return "";

                    return result;
                }
            }
            catch (Exception)
            {
                // ignored
            }

            return "";
        }
    }
}
